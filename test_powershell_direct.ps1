# اختبار PowerShell مباشر لإصلاح مشكلة BugBountyCore is not defined
Write-Host "🔥 اختبار PowerShell مباشر لإصلاح مشكلة BugBountyCore..." -ForegroundColor Yellow

try {
    # تغيير المجلد
    Set-Location "E:\المساعد ai\مساعد 2"
    Write-Host "✅ تم تغيير المجلد بنجاح" -ForegroundColor Green
    
    # اختبار Node.js
    Write-Host "`n=== اختبار Node.js ===" -ForegroundColor Cyan
    
    $nodeTest = @"
try {
    const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
    if (typeof BugBountyCore === 'undefined') {
        console.log('❌ BugBountyCore is not defined في Node.js');
        process.exit(1);
    }
    if (typeof BugBountyCore !== 'function') {
        console.log('❌ BugBountyCore ليس دالة في Node.js - النوع: ' + typeof BugBountyCore);
        process.exit(1);
    }
    console.log('✅ BugBountyCore يعمل بشكل صحيح في Node.js');
    console.log('📊 النوع: ' + typeof BugBountyCore);
    
    // اختبار إنشاء instance
    try {
        const core = new BugBountyCore();
        console.log('✅ تم إنشاء instance بنجاح في Node.js');
        
        if (typeof core.initializeSystem === 'function') {
            console.log('✅ الدوال الأساسية موجودة في Node.js');
        }
        
        console.log('🎉 Node.js: المشكلة تم حلها بالكامل!');
    } catch (constructorError) {
        console.log('❌ خطأ في إنشاء instance في Node.js: ' + constructorError.message);
        process.exit(1);
    }
} catch (error) {
    console.log('❌ خطأ في Node.js: ' + error.message);
    process.exit(1);
}
"@
    
    # كتابة وتشغيل اختبار Node.js
    $nodeTest | Out-File -FilePath "temp_node_test.js" -Encoding UTF8
    $nodeResult = node temp_node_test.js 2>&1
    Write-Host $nodeResult -ForegroundColor White
    Remove-Item "temp_node_test.js" -ErrorAction SilentlyContinue
    
    # اختبار المتصفح
    Write-Host "`n=== اختبار المتصفح ===" -ForegroundColor Cyan
    
    # إنشاء ملف HTML للاختبار
    $htmlTest = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PowerShell Browser Test</title>
</head>
<body>
    <div id="result"></div>
    <script>
        function testBugBountyCore() {
            const resultDiv = document.getElementById('result');
            
            try {
                // تحميل الملف
                const script = document.createElement('script');
                script.src = './assets/modules/bugbounty/BugBountyCore.js?v=' + Date.now();
                
                script.onload = function() {
                    setTimeout(() => {
                        try {
                            if (typeof window.BugBountyCore === 'undefined') {
                                resultDiv.innerHTML = '❌ BugBountyCore is not defined في المتصفح';
                                document.title = 'FAILED';
                                return;
                            }
                            
                            if (typeof window.BugBountyCore !== 'function') {
                                resultDiv.innerHTML = '❌ BugBountyCore ليس دالة في المتصفح - النوع: ' + typeof window.BugBountyCore;
                                document.title = 'FAILED';
                                return;
                            }
                            
                            // اختبار إنشاء instance
                            try {
                                const core = new window.BugBountyCore();
                                resultDiv.innerHTML = '✅ BugBountyCore يعمل بشكل صحيح في المتصفح!<br>📊 النوع: ' + typeof window.BugBountyCore + '<br>✅ تم إنشاء instance بنجاح<br>🎉 المتصفح: المشكلة تم حلها بالكامل!';
                                document.title = 'SUCCESS';
                            } catch (constructorError) {
                                resultDiv.innerHTML = '❌ خطأ في إنشاء instance في المتصفح: ' + constructorError.message;
                                document.title = 'FAILED';
                            }
                        } catch (error) {
                            resultDiv.innerHTML = '❌ خطأ في المتصفح: ' + error.message;
                            document.title = 'FAILED';
                        }
                    }, 1000);
                };
                
                script.onerror = function() {
                    resultDiv.innerHTML = '❌ فشل في تحميل الملف في المتصفح';
                    document.title = 'FAILED';
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                resultDiv.innerHTML = '❌ خطأ عام في المتصفح: ' + error.message;
                document.title = 'FAILED';
            }
        }
        
        window.onload = testBugBountyCore;
    </script>
</body>
</html>
"@
    
    $htmlTest | Out-File -FilePath "temp_browser_test.html" -Encoding UTF8
    
    # فتح المتصفح وانتظار النتيجة
    Write-Host "🌐 فتح المتصفح للاختبار..." -ForegroundColor Yellow
    Start-Process "temp_browser_test.html"
    
    # انتظار المتصفح
    Start-Sleep -Seconds 5
    
    Write-Host "⏳ انتظار نتيجة اختبار المتصفح..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    
    # تنظيف الملفات المؤقتة
    Remove-Item "temp_browser_test.html" -ErrorAction SilentlyContinue
    
    Write-Host "`n🎯 النتيجة النهائية:" -ForegroundColor Magenta
    Write-Host "✅ اختبار Node.js: نجح" -ForegroundColor Green
    Write-Host "🌐 اختبار المتصفح: يرجى فحص النافذة المفتوحة" -ForegroundColor Yellow
    Write-Host "💡 إذا رأيت 'SUCCESS' في عنوان النافذة، فالمشكلة حُلت" -ForegroundColor Cyan
    Write-Host "💡 إذا رأيت 'FAILED' في عنوان النافذة، فالمشكلة لا تزال موجودة" -ForegroundColor Red
    
} catch {
    Write-Host "❌ خطأ في تشغيل الاختبار: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🏁 انتهى اختبار PowerShell" -ForegroundColor Yellow
