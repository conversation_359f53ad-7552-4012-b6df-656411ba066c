const fs = require('fs');

console.log('🔍 اختبار عرض الصور من المجلد بدلاً من base64...\n');

// تشغيل النظام لإنتاج تقرير جديد
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testImageDisplay() {
    try {
        console.log('🚀 بدء اختبار عرض الصور...');

        const bugBounty = new BugBountyCore();
        
        // تشغيل فحص سريع
        const result = await bugBounty.startComprehensiveScan('http://testphp.vulnweb.com', {
            maxVulnerabilities: 5, // فحص سريع
            includeScreenshots: true,
            useRealImages: true
        });
        
        if (result && result.report) {
            console.log('✅ تم إنتاج التقرير بنجاح');
            
            // فحص نوع الصور في التقرير
            const base64Images = (result.report.match(/data:image/g) || []).length;
            const pathImages = (result.report.match(/\.\/assets\/modules\/bugbounty\/screenshots/g) || []).length;
            
            console.log(`📊 إحصائيات الصور:`);
            console.log(`   - صور base64: ${base64Images}`);
            console.log(`   - صور من مسارات: ${pathImages}`);
            
            if (pathImages > base64Images) {
                console.log('🎉 نجح الإصلاح! النظام يستخدم مسارات الصور بدلاً من base64');
            } else {
                console.log('⚠️ لا يزال النظام يستخدم base64 أكثر من المسارات');
            }
            
            // حفظ عينة من التقرير للفحص
            const sampleReport = result.report.substring(0, 5000);
            fs.writeFileSync('sample_report_images.html', sampleReport);
            console.log('📄 تم حفظ عينة من التقرير في: sample_report_images.html');
            
        } else {
            console.log('❌ فشل في إنتاج التقرير');
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
    }
}

testImageDisplay();
