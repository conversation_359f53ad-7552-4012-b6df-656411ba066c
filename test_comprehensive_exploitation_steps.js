const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

console.log('🚀 اختبار خطوات الاستغلال الشاملة التفصيلية...\n');

async function testComprehensiveExploitationSteps() {
    try {
        const bugBounty = new BugBountyCore();
        
        console.log('📋 إنشاء ثغرات اختبار مختلفة...');
        
        // ثغرات اختبار مختلفة
        const testVulnerabilities = [
            {
                name: 'SQL Injection in Login Form',
                type: 'SQL Injection',
                severity: 'High',
                url: 'http://testphp.vulnweb.com/login.php',
                parameter: 'username',
                payload: "admin' OR '1'='1' --",
                response: 'Login successful - Welcome admin',
                evidence: 'تم تجاوز المصادقة بنجاح'
            },
            {
                name: 'Reflected XSS in Search',
                type: 'XSS',
                severity: 'Medium',
                url: 'http://testphp.vulnweb.com/search.php',
                parameter: 'q',
                payload: "<script>alert('XSS')</script>",
                response: 'Script executed in browser',
                evidence: 'تم تنفيذ JavaScript في المتصفح'
            },
            {
                name: 'Command Injection in File Upload',
                type: 'Command Injection',
                severity: 'Critical',
                url: 'http://testphp.vulnweb.com/upload.php',
                parameter: 'filename',
                payload: "; whoami; pwd",
                response: 'www-data /var/www/html',
                evidence: 'تم تنفيذ أوامر النظام'
            },
            {
                name: 'IDOR in User Profile',
                type: 'IDOR',
                severity: 'Medium',
                url: 'http://testphp.vulnweb.com/profile.php',
                parameter: 'user_id',
                payload: "2",
                response: 'User profile data for user 2',
                evidence: 'تم الوصول لبيانات مستخدم آخر'
            }
        ];
        
        console.log('🎯 اختبار دالة generateRealExploitationStepsForVulnerabilityComprehensive...\n');
        
        const results = [];
        
        for (let i = 0; i < testVulnerabilities.length; i++) {
            const vuln = testVulnerabilities[i];
            console.log(`📊 اختبار الثغرة ${i + 1}: ${vuln.name}`);
            
            // إنشاء بيانات استغلال وهمية
            const exploitationResults = {
                payload: vuln.payload,
                response: vuln.response,
                evidence: vuln.evidence,
                parameter: vuln.parameter,
                success: true
            };
            
            // استدعاء الدالة المحسنة
            const exploitationSteps = bugBounty.generateRealExploitationStepsForVulnerabilityComprehensive(vuln, exploitationResults);
            
            console.log(`✅ تم إنتاج خطوات الاستغلال: ${exploitationSteps.length} حرف`);
            
            // فحص المحتوى
            const hasDetailedSteps = exploitationSteps.includes('خطوات الاستغلال التفصيلية');
            const hasEvidence = exploitationSteps.includes('أدلة الاستغلال');
            const hasTechnicalProof = exploitationSteps.includes('الدليل التقني');
            const hasTimeline = exploitationSteps.includes('الجدول الزمني');
            const hasSpecificPayload = exploitationSteps.includes(vuln.payload);
            
            results.push({
                vulnerability: vuln.name,
                type: vuln.type,
                contentLength: exploitationSteps.length,
                hasDetailedSteps,
                hasEvidence,
                hasTechnicalProof,
                hasTimeline,
                hasSpecificPayload,
                content: exploitationSteps
            });
            
            console.log(`   - خطوات تفصيلية: ${hasDetailedSteps ? '✅' : '❌'}`);
            console.log(`   - أدلة: ${hasEvidence ? '✅' : '❌'}`);
            console.log(`   - دليل تقني: ${hasTechnicalProof ? '✅' : '❌'}`);
            console.log(`   - جدول زمني: ${hasTimeline ? '✅' : '❌'}`);
            console.log(`   - Payload محدد: ${hasSpecificPayload ? '✅' : '❌'}\n`);
        }
        
        // إنشاء تقرير HTML للنتائج
        const reportHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>اختبار خطوات الاستغلال الشاملة التفصيلية</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .vulnerability-card { 
            background: white; 
            margin: 20px 0; 
            border-radius: 10px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .vuln-header { 
            background: linear-gradient(135deg, #3498db, #2980b9); 
            color: white; 
            padding: 15px; 
            font-weight: bold; 
        }
        .vuln-stats { 
            background: #ecf0f1; 
            padding: 15px; 
            display: flex; 
            justify-content: space-between; 
            flex-wrap: wrap; 
        }
        .stat { 
            background: white; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 5px; 
            text-align: center; 
            min-width: 120px; 
        }
        .content-preview { 
            padding: 20px; 
            max-height: 400px; 
            overflow-y: auto; 
            border-top: 1px solid #ddd; 
        }
        .success { color: #27ae60; font-weight: bold; }
        .error { color: #e74c3c; font-weight: bold; }
        .summary { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 20px 0; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 اختبار خطوات الاستغلال الشاملة التفصيلية</h1>
            <p>Bug Bounty v4.0 - generateRealExploitationStepsForVulnerabilityComprehensive()</p>
        </div>
        
        <div class="summary">
            <h2>📊 ملخص النتائج</h2>
            <p><strong>إجمالي الثغرات المختبرة:</strong> ${results.length}</p>
            <p><strong>متوسط طول المحتوى:</strong> ${Math.round(results.reduce((sum, r) => sum + r.contentLength, 0) / results.length)} حرف</p>
            <p><strong>الثغرات مع خطوات تفصيلية:</strong> ${results.filter(r => r.hasDetailedSteps).length}/${results.length}</p>
            <p><strong>الثغرات مع أدلة:</strong> ${results.filter(r => r.hasEvidence).length}/${results.length}</p>
            <p><strong>الثغرات مع دليل تقني:</strong> ${results.filter(r => r.hasTechnicalProof).length}/${results.length}</p>
        </div>
        
        ${results.map((result, index) => `
            <div class="vulnerability-card">
                <div class="vuln-header">
                    🎯 الثغرة ${index + 1}: ${result.vulnerability}
                    <span style="float: right; background: rgba(255,255,255,0.2); padding: 5px 10px; border-radius: 15px; font-size: 0.9em;">
                        ${result.type}
                    </span>
                </div>
                
                <div class="vuln-stats">
                    <div class="stat">
                        <div>طول المحتوى</div>
                        <div style="font-weight: bold; color: #3498db;">${result.contentLength} حرف</div>
                    </div>
                    <div class="stat">
                        <div>خطوات تفصيلية</div>
                        <div class="${result.hasDetailedSteps ? 'success' : 'error'}">${result.hasDetailedSteps ? '✅ نعم' : '❌ لا'}</div>
                    </div>
                    <div class="stat">
                        <div>أدلة</div>
                        <div class="${result.hasEvidence ? 'success' : 'error'}">${result.hasEvidence ? '✅ نعم' : '❌ لا'}</div>
                    </div>
                    <div class="stat">
                        <div>دليل تقني</div>
                        <div class="${result.hasTechnicalProof ? 'success' : 'error'}">${result.hasTechnicalProof ? '✅ نعم' : '❌ لا'}</div>
                    </div>
                    <div class="stat">
                        <div>جدول زمني</div>
                        <div class="${result.hasTimeline ? 'success' : 'error'}">${result.hasTimeline ? '✅ نعم' : '❌ لا'}</div>
                    </div>
                    <div class="stat">
                        <div>Payload محدد</div>
                        <div class="${result.hasSpecificPayload ? 'success' : 'error'}">${result.hasSpecificPayload ? '✅ نعم' : '❌ لا'}</div>
                    </div>
                </div>
                
                <div class="content-preview">
                    <h4>📄 معاينة المحتوى:</h4>
                    ${result.content}
                </div>
            </div>
        `).join('')}
    </div>
</body>
</html>
        `;
        
        // حفظ التقرير
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const reportName = `Comprehensive_Exploitation_Steps_Test_${timestamp}.html`;
        fs.writeFileSync(reportName, reportHTML);
        
        console.log(`📄 تم حفظ تقرير الاختبار: ${reportName}`);
        
        // ملخص النتائج
        console.log('\n📊 ملخص نتائج الاختبار:');
        console.log(`✅ تم اختبار ${results.length} ثغرة`);
        console.log(`📏 متوسط طول المحتوى: ${Math.round(results.reduce((sum, r) => sum + r.contentLength, 0) / results.length)} حرف`);
        console.log(`📋 خطوات تفصيلية: ${results.filter(r => r.hasDetailedSteps).length}/${results.length}`);
        console.log(`🔍 أدلة: ${results.filter(r => r.hasEvidence).length}/${results.length}`);
        console.log(`🔬 دليل تقني: ${results.filter(r => r.hasTechnicalProof).length}/${results.length}`);
        
        const allHaveDetailedSteps = results.every(r => r.hasDetailedSteps);
        const allHaveEvidence = results.every(r => r.hasEvidence);
        const allHaveTechnicalProof = results.every(r => r.hasTechnicalProof);
        
        if (allHaveDetailedSteps && allHaveEvidence && allHaveTechnicalProof) {
            console.log('\n🎉 نجح الاختبار! جميع الثغرات تحتوي على خطوات استغلال شاملة تفصيلية');
        } else {
            console.log('\n⚠️ الاختبار يحتاج تحسين - بعض الثغرات لا تحتوي على تفاصيل كاملة');
        }
        
        return {
            success: true,
            reportName,
            results,
            summary: {
                totalVulnerabilities: results.length,
                averageContentLength: Math.round(results.reduce((sum, r) => sum + r.contentLength, 0) / results.length),
                withDetailedSteps: results.filter(r => r.hasDetailedSteps).length,
                withEvidence: results.filter(r => r.hasEvidence).length,
                withTechnicalProof: results.filter(r => r.hasTechnicalProof).length
            }
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        return { success: false, error: error.message };
    }
}

testComprehensiveExploitationSteps().then(result => {
    if (result.success) {
        console.log(`\n🎯 تم الانتهاء من الاختبار! افتح التقرير: ${result.reportName}`);
    }
});
