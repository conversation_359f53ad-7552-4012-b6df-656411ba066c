const fs = require('fs');

console.log('🔧 إصلاح عرض الصور بشكل صحيح...\n');

// قراءة ملف BugBountyCore.js
const filePath = 'assets/modules/bugbounty/BugBountyCore.js';
let content = fs.readFileSync(filePath, 'utf8');

console.log('📊 إحصائيات قبل الإصلاح:');
const wrongPatterns = content.match(/data:image\/png;base64,\$\{.*?screenshots.*?\}/g);
console.log(`   - أنماط خاطئة: ${wrongPatterns ? wrongPatterns.length : 0}`);

// الإصلاح الصحيح
let fixCount = 0;

// 1. إصلاح الأنماط الخاطئة في generateRealScreenshotHTML
console.log('\n🔧 إصلاح الأنماط الخاطئة...');

// البحث عن جميع الأنماط الخاطئة وإصلاحها
const wrongPattern1 = /src="data:image\/png;base64,\$\{([^}]*screenshots[^}]*)\}"/g;
content = content.replace(wrongPattern1, (match, pathVar) => {
    fixCount++;
    console.log(`   - إصلاح: ${match.substring(0, 50)}...`);
    return `src="\${${pathVar}}"`;
});

// 2. إصلاح الأنماط الخاطئة الأخرى
const wrongPattern2 = /src="data:image\/png;base64,([^"]*screenshots[^"]*)"/g;
content = content.replace(wrongPattern2, (match, path) => {
    fixCount++;
    console.log(`   - إصلاح: ${match.substring(0, 50)}...`);
    return `src="${path}"`;
});

// 3. إصلاح الأنماط المختلطة
const wrongPattern3 = /<img src="data:image\/png;base64,\$\{([^}]*screenshots[^}]*)\}"/g;
content = content.replace(wrongPattern3, (match, pathVar) => {
    fixCount++;
    console.log(`   - إصلاح: ${match.substring(0, 50)}...`);
    return `<img src="\${${pathVar}}"`;
});

// 4. إصلاح أي أنماط متبقية
const wrongPattern4 = /data:image\/png;base64,(\$\{[^}]*screenshots[^}]*\})/g;
content = content.replace(wrongPattern4, (match, pathVar) => {
    fixCount++;
    console.log(`   - إصلاح: ${match.substring(0, 50)}...`);
    return pathVar;
});

// حفظ الملف المُحدث
fs.writeFileSync(filePath, content);

console.log('\n📊 إحصائيات بعد الإصلاح:');
const remainingWrong = content.match(/data:image\/png;base64,.*?screenshots/g);
console.log(`   - أنماط خاطئة متبقية: ${remainingWrong ? remainingWrong.length : 0}`);
console.log(`   - عدد الإصلاحات المطبقة: ${fixCount}`);

if (remainingWrong && remainingWrong.length > 0) {
    console.log('\n⚠️ أنماط خاطئة متبقية:');
    remainingWrong.slice(0, 3).forEach((pattern, i) => {
        console.log(`   ${i + 1}. ${pattern.substring(0, 80)}...`);
    });
}

console.log('\n🎉 تم إصلاح عرض الصور بشكل صحيح!');
console.log('📝 الآن الصور ستُعرض من المجلد مباشرة بدون base64');

// اختبار سريع للتأكد من الإصلاح
const testPattern = content.match(/src="\.\/assets\/modules\/bugbounty\/screenshots/g);
console.log(`\n✅ مسارات صور صحيحة: ${testPattern ? testPattern.length : 0}`);
