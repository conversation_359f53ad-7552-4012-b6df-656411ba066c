// اختبار سريع لإصلاح الصور
const fs = require('fs');

async function testImagesFix() {
    try {
        console.log('🔥 اختبار إصلاح الصور...');
        
        // تحميل النظام
        const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
        const core = new BugBountyCore();
        
        // إنشاء ثغرة تجريبية
        const testVuln = {
            name: 'SQL Injection في صفحة تسجيل الدخول',
            type: 'SQL Injection',
            url: 'http://testphp.vulnweb.com/login.php',
            screenshots: true // إضافة هذا لتفعيل البحث
        };
        
        console.log('\n🔍 اختبار دالة findRealImageForVulnerability...');
        
        // اختبار البحث عن الصور
        const stages = ['before', 'during', 'after'];
        const results = {};
        
        for (const stage of stages) {
            console.log(`\n--- اختبار ${stage} ---`);
            try {
                const result = core.findRealImageForVulnerability(testVuln, stage);
                results[stage] = {
                    success: !!result,
                    result: result,
                    isPath: result && result.startsWith('./assets'),
                    isBase64: result && result.startsWith('data:image'),
                    length: result ? result.length : 0
                };
                
                console.log(`✅ النتيجة: ${result ? 'موجود' : 'غير موجود'}`);
                if (result) {
                    console.log(`📊 النوع: ${results[stage].isPath ? 'مسار ملف' : results[stage].isBase64 ? 'Base64' : 'غير معروف'}`);
                    console.log(`📏 الطول: ${result.length} حرف`);
                    console.log(`🔍 البداية: ${result.substring(0, 50)}...`);
                }
            } catch (error) {
                results[stage] = {
                    success: false,
                    error: error.message
                };
                console.log(`❌ خطأ: ${error.message}`);
            }
        }
        
        console.log('\n🎨 اختبار إنشاء HTML للصور...');
        
        // اختبار إنشاء HTML للصور
        const htmlResults = {};
        for (const stage of stages) {
            try {
                const html = core.generateVulnerabilityImageHTML(testVuln, stage);
                htmlResults[stage] = {
                    success: !!html,
                    hasImage: html && html.includes('src='),
                    hasRealPath: html && html.includes('./assets/modules/bugbounty/screenshots/'),
                    length: html ? html.length : 0
                };
                
                console.log(`${stage}: ${htmlResults[stage].success ? '✅' : '❌'} HTML (${htmlResults[stage].length} chars)`);
                if (htmlResults[stage].hasRealPath) {
                    console.log(`  📷 يحتوي على مسار صورة حقيقية`);
                }
            } catch (error) {
                htmlResults[stage] = {
                    success: false,
                    error: error.message
                };
                console.log(`${stage}: ❌ خطأ - ${error.message}`);
            }
        }
        
        console.log('\n📄 اختبار إنشاء تقرير مع الصور...');
        
        // إنشاء تقرير تجريبي
        const pageData = {
            page_name: 'اختبار إصلاح الصور',
            page_title: 'اختبار إصلاح الصور',
            page_url: 'http://testphp.vulnweb.com',
            vulnerabilities: [testVuln],
            scan_date: new Date().toLocaleString('ar-SA'),
            total_vulnerabilities: 1,
            critical_count: 1,
            high_count: 0,
            exploitation_confirmed: 1
        };
        
        const report = await core.formatSinglePageReport(pageData);
        
        console.log(`📏 حجم التقرير: ${report.length} حرف`);
        
        // فحص محتوى التقرير للصور
        const hasRealImagePaths = report.includes('./assets/modules/bugbounty/screenshots/');
        const hasBase64Images = report.includes('src="data:image/png;base64,') && 
                               !report.includes('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
        const hasFakeImages = report.includes('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
        
        console.log(`📊 التقرير يحتوي على:`);
        console.log(`  📁 مسارات صور حقيقية: ${hasRealImagePaths ? '✅' : '❌'}`);
        console.log(`  📷 صور Base64 حقيقية: ${hasBase64Images ? '✅' : '❌'}`);
        console.log(`  🚫 صور وهمية: ${hasFakeImages ? '❌ موجودة' : '✅ غير موجودة'}`);
        
        // حفظ التقرير للفحص
        const reportFile = `Image_Fix_Test_${Date.now()}.html`;
        fs.writeFileSync(reportFile, report);
        console.log(`💾 تم حفظ التقرير: ${reportFile}`);
        
        // النتائج النهائية
        console.log('\n🏆 النتائج النهائية:');
        
        const findSuccess = Object.values(results).filter(r => r.success).length;
        const htmlSuccess = Object.values(htmlResults).filter(r => r.success).length;
        const realImagesInReport = hasRealImagePaths || hasBase64Images;
        
        console.log(`🔍 دالة البحث: ${findSuccess}/3 مراحل تعمل`);
        console.log(`🎨 إنشاء HTML: ${htmlSuccess}/3 مراحل تعمل`);
        console.log(`📄 التقرير: ${realImagesInReport ? '✅ يحتوي على صور حقيقية' : '❌ لا يحتوي على صور حقيقية'}`);
        console.log(`🚫 صور وهمية: ${hasFakeImages ? '❌ موجودة' : '✅ تم إزالتها'}`);
        
        const overallSuccess = findSuccess === 3 && htmlSuccess === 3 && realImagesInReport && !hasFakeImages;
        console.log(`🎯 النتيجة الإجمالية: ${overallSuccess ? '🎉 100% نجح!' : '⚠️ يحتاج مزيد من الإصلاح'}`);
        
        return {
            success: overallSuccess,
            findSuccess: findSuccess,
            htmlSuccess: htmlSuccess,
            realImagesInReport: realImagesInReport,
            noFakeImages: !hasFakeImages,
            reportFile: reportFile
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار إصلاح الصور:', error.message);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
testImagesFix().then(result => {
    if (result.success) {
        console.log('\n🎊 تم إصلاح مشكلة الصور بنجاح! النسبة 100%!');
    } else {
        console.log(`\n❌ لا تزال هناك مشاكل في الصور: ${result.error || 'مشاكل متعددة'}`);
    }
}).catch(error => {
    console.error('❌ خطأ في تشغيل اختبار إصلاح الصور:', error);
});
