const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

console.log('🚀 اختبار إنتاج تقرير بسيط مع الصور المُصلحة...\n');

async function testSimpleReport() {
    try {
        const bugBounty = new BugBountyCore();
        
        // إنشاء ثغرة وهمية للاختبار
        const testVulnerability = {
            name: 'Test XSS',
            type: 'XSS',
            severity: 'High',
            description: 'اختبار عرض الصور',
            screenshots: {
                before: './assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/before_XSS.png',
                during: './assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/during_XSS.png',
                after: './assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/after_XSS.png'
            }
        };
        
        console.log('📋 إنشاء تقرير بسيط...');
        
        // استخدام دالة إنشاء HTML للصور
        const imageHTML = bugBounty.generateRealScreenshotHTML(testVulnerability);
        
        // إنشاء تقرير HTML بسيط
        const reportHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>اختبار عرض الصور</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .vulnerability { border: 1px solid #ddd; padding: 20px; margin: 20px 0; }
        .screenshot-section { margin: 15px 0; }
        img { max-width: 300px; border: 2px solid #28a745; margin: 10px; }
    </style>
</head>
<body>
    <h1>🔍 اختبار عرض الصور من المجلد</h1>
    
    <div class="vulnerability">
        <h2>🎯 ثغرة اختبار: ${testVulnerability.name}</h2>
        <p><strong>النوع:</strong> ${testVulnerability.type}</p>
        <p><strong>الخطورة:</strong> ${testVulnerability.severity}</p>
        
        <div class="screenshot-section">
            <h3>📸 الصور:</h3>
            ${imageHTML}
        </div>
        
        <div class="screenshot-section">
            <h3>🧪 اختبار مباشر للصور:</h3>
            <div>
                <h4>📷 قبل الاستغلال:</h4>
                <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/before_XSS.png" 
                     alt="قبل الاستغلال" 
                     onload="console.log('✅ تم تحميل صورة قبل الاستغلال')"
                     onerror="console.log('❌ فشل تحميل صورة قبل الاستغلال')">
            </div>
            
            <div>
                <h4>⚡ أثناء الاستغلال:</h4>
                <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/during_XSS.png" 
                     alt="أثناء الاستغلال"
                     onload="console.log('✅ تم تحميل صورة أثناء الاستغلال')"
                     onerror="console.log('❌ فشل تحميل صورة أثناء الاستغلال')">
            </div>
            
            <div>
                <h4>🚨 بعد الاستغلال:</h4>
                <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/after_XSS.png" 
                     alt="بعد الاستغلال"
                     onload="console.log('✅ تم تحميل صورة بعد الاستغلال')"
                     onerror="console.log('❌ فشل تحميل صورة بعد الاستغلال')">
            </div>
        </div>
    </div>
    
    <script>
        console.log('🔍 اختبار تحميل الصور...');
        
        // فحص جميع الصور
        document.querySelectorAll('img').forEach((img, index) => {
            img.onload = () => console.log(\`✅ تم تحميل الصورة \${index + 1} بنجاح من: \${img.src}\`);
            img.onerror = () => console.log(\`❌ فشل تحميل الصورة \${index + 1} من: \${img.src}\`);
        });
        
        // فحص وجود الملفات
        setTimeout(() => {
            console.log('📊 ملخص تحميل الصور:');
            const loadedImages = document.querySelectorAll('img[src*="screenshots"]').length;
            console.log(\`   - إجمالي الصور: \${loadedImages}\`);
        }, 2000);
    </script>
</body>
</html>
        `;
        
        // حفظ التقرير
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const reportName = `Simple_Image_Test_${timestamp}.html`;

        fs.writeFileSync(reportName, reportHTML);
        console.log(`\n📄 تم حفظ التقرير البسيط: ${reportName}`);
        
        // فحص محتوى HTML المُنتج
        console.log('\n🔍 فحص محتوى HTML المُنتج:');
        const base64Count = (imageHTML.match(/data:image/g) || []).length;
        const pathCount = (imageHTML.match(/\.\/assets\/modules\/bugbounty\/screenshots/g) || []).length;
        
        console.log(`   - صور base64: ${base64Count}`);
        console.log(`   - صور من مسارات: ${pathCount}`);
        
        if (pathCount > base64Count) {
            console.log('🎉 نجح الإصلاح! النظام يستخدم مسارات الصور');
        } else {
            console.log('⚠️ النظام لا يزال يستخدم base64');
        }
        
        return reportName;
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        return null;
    }
}

testSimpleReport().then(reportName => {
    if (reportName) {
        console.log(`\n🎯 تم الانتهاء! افتح التقرير: ${reportName}`);
        console.log('🌐 أو افتحه في المتصفح لاختبار عرض الصور');
    }
});
