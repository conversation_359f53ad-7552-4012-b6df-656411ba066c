const fs = require('fs');

console.log('🔍 فحص محتوى التقرير الفعلي...\n');

try {
    const reportContent = fs.readFileSync('Bug_Bounty_Page_1_testphp_vulnweb_com_20250717T154335.html', 'utf8');
    
    // النقطة الأولى: عدد الدوال الـ36
    const functionMatches = reportContent.match(/Function \d+:/g);
    console.log(`✅ النقطة الأولى - عدد الدوال: ${functionMatches ? functionMatches.length : 0}`);
    
    // النقطة الثانية: المحتوى الديناميكي
    const dynamicContent = reportContent.includes('testphp.vulnweb.com');
    console.log(`✅ النقطة الثانية - المحتوى الديناميكي: ${dynamicContent ? 'موجود' : 'غير موجود'}`);
    
    // النقطة الثالثة: الصور الفعلية
    const imageMatches = reportContent.match(/data:image/g);
    console.log(`✅ النقطة الثالثة - عدد الصور: ${imageMatches ? imageMatches.length : 0}`);
    
    // النقطة الرابعة: الملفات الشاملة
    const comprehensiveFiles = reportContent.includes('comprehensive-files');
    console.log(`✅ النقطة الرابعة - الملفات الشاملة: ${comprehensiveFiles ? 'موجودة' : 'غير موجودة'}`);
    
    // النقطة الخامسة: تفاصيل الثغرات
    const vulnerabilityDetails = reportContent.match(/API Authentication Bypass|XSS|SQL Injection/g);
    console.log(`✅ النقطة الخامسة - تفاصيل الثغرات: ${vulnerabilityDetails ? vulnerabilityDetails.length : 0} ثغرة مكتشفة`);
    
    // التحقق من التقرير المنفصل
    const separateReportExists = fs.existsSync('Bug_Bounty_Page_1_testphp_vulnweb_com_20250717T154335_separate.html');
    console.log(`📄 التقرير المنفصل: ${separateReportExists ? 'موجود' : 'غير موجود'}`);
    
    // فحص حجم التقرير
    const stats = fs.statSync('Bug_Bounty_Page_1_testphp_vulnweb_com_20250717T154335.html');
    console.log(`📊 حجم التقرير: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
    
    // فحص عينة من المحتوى
    console.log('\n🔍 عينة من محتوى التقرير:');
    const sampleContent = reportContent.substring(1000, 2000);
    console.log(sampleContent.substring(0, 500) + '...');
    
} catch (error) {
    console.error('❌ خطأ في قراءة التقرير:', error.message);
}
