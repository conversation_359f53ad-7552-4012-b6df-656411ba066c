/**
 * Textual Impact Analyzer v4.0
 * تحليل التأثير النصي والسلوكي للثغرات الأمنية
 * يتكامل مع ImpactVisualizer لتوفير تحليل شامل للتغيرات
 * 
 * @version 4.0.0
 * <AUTHOR> Assistant AI
 * @description محلل التأثير النصي المتقدم للثغرات الأمنية
 */

console.log('🔧 تحميل TextualImpactAnalyzer v4.0...');

class TextualImpactAnalyzer {
    constructor() {
        this.version = '4.0';
        this.systemName = 'Textual Impact Analyzer v4.0';
        this.analysisResults = [];
        this.domComparisonEngine = this.initializeDOMComparisonEngine();
        this.behavioralAnalysisEngine = this.initializeBehavioralAnalysisEngine();
        this.textualChangeDetector = this.initializeTextualChangeDetector();
        
        console.log(`✅ ${this.systemName} تم تحميله بنجاح`);
        console.log('🎯 ميزات v4.0: تحليل نصي متقدم + كشف تغيرات DOM + تحليل سلوكي');
    }

    // تهيئة محرك مقارنة DOM
    initializeDOMComparisonEngine() {
        return {
            compareBeforeAfter: (beforeDOM, afterDOM) => {
                return this.performDOMComparison(beforeDOM, afterDOM);
            },
            detectNewElements: (beforeDOM, afterDOM) => {
                return this.detectNewDOMElements(beforeDOM, afterDOM);
            },
            detectRemovedElements: (beforeDOM, afterDOM) => {
                return this.detectRemovedDOMElements(beforeDOM, afterDOM);
            },
            detectModifiedElements: (beforeDOM, afterDOM) => {
                return this.detectModifiedDOMElements(beforeDOM, afterDOM);
            }
        };
    }

    // تهيئة محرك التحليل السلوكي
    initializeBehavioralAnalysisEngine() {
        return {
            analyzeResponseTime: (beforeTime, afterTime) => {
                return this.analyzeBehavioralResponseTime(beforeTime, afterTime);
            },
            detectErrorMessages: (responseText) => {
                return this.detectErrorMessagesInResponse(responseText);
            },
            analyzeContentChanges: (beforeContent, afterContent) => {
                return this.analyzeBehavioralContentChanges(beforeContent, afterContent);
            },
            detectSecurityBypass: (vulnerability, responseData) => {
                return this.detectSecurityBypassBehavior(vulnerability, responseData);
            }
        };
    }

    // تهيئة كاشف التغيرات النصية
    initializeTextualChangeDetector() {
        return {
            extractErrorMessages: (text) => {
                return this.extractErrorMessagesFromText(text);
            },
            detectDataExposure: (text, vulnerability) => {
                return this.detectDataExposureInText(text, vulnerability);
            },
            analyzeTextualChanges: (beforeText, afterText) => {
                return this.analyzeTextualDifferences(beforeText, afterText);
            },
            detectInjectionEffects: (text, payload) => {
                return this.detectInjectionEffectsInText(text, payload);
            }
        };
    }

    // الدالة الرئيسية لتحليل تفاصيل الثغرة
    async analyzeVulnerabilityDetails(vulnerability, websiteData, exploitationResult) {
        console.log(`🔬 تحليل تفاصيل الثغرة: ${vulnerability.name}`);

        const analysis = {
            vulnerability_name: vulnerability.name,
            vulnerability_type: vulnerability.category,
            timestamp: new Date().toISOString(),
            
            // التحليل النصي المتقدم
            textual_analysis: await this.performTextualAnalysis(vulnerability, websiteData, exploitationResult),
            
            // تحليل تغيرات DOM
            dom_analysis: await this.performDOMAnalysis(vulnerability, websiteData, exploitationResult),
            
            // التحليل السلوكي
            behavioral_analysis: await this.performBehavioralAnalysis(vulnerability, websiteData, exploitationResult),
            
            // تحليل التأثير على النظام
            system_impact_analysis: await this.performSystemImpactAnalysis(vulnerability, websiteData, exploitationResult),
            
            // تحليل الأدوات والمصادر
            tools_and_sources: await this.analyzeToolsAndSources(vulnerability, exploitationResult),
            
            // تحليل التقييم والمثابرة
            persistence_analysis: await this.analyzePersistenceAndReproducibility(vulnerability, exploitationResult)
        };

        this.analysisResults.push(analysis);
        return analysis;
    }

    // تحليل نصي متقدم
    async performTextualAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('📝 تحليل نصي متقدم...');

        const textualAnalysis = {
            description: this.generateDynamicDescription(vulnerability, exploitationResult),
            payloads_used: this.extractPayloadsUsed(vulnerability, exploitationResult),
            error_messages_detected: this.extractErrorMessages(exploitationResult),
            data_exposure_detected: this.analyzeDataExposure(vulnerability, exploitationResult),
            textual_changes: this.analyzeTextualChanges(vulnerability, exploitationResult),
            injection_effects: this.analyzeInjectionEffects(vulnerability, exploitationResult)
        };

        return textualAnalysis;
    }

    // تحليل تغيرات DOM
    async performDOMAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('🌐 تحليل تغيرات DOM...');

        const domAnalysis = {
            dom_changes: this.analyzeDOMChanges(vulnerability, exploitationResult),
            new_elements_added: this.detectNewElements(exploitationResult),
            elements_modified: this.detectModifiedElements(exploitationResult),
            elements_removed: this.detectRemovedElements(exploitationResult),
            layout_changes: this.analyzeLayoutChanges(vulnerability, exploitationResult),
            style_modifications: this.analyzeStyleModifications(exploitationResult)
        };

        return domAnalysis;
    }

    // التحليل السلوكي
    async performBehavioralAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('🎭 التحليل السلوكي...');

        const behavioralAnalysis = {
            response_time_changes: this.analyzeResponseTimeChanges(exploitationResult),
            server_behavior_changes: this.analyzeServerBehaviorChanges(vulnerability, exploitationResult),
            session_impact: this.analyzeSessionImpact(vulnerability, exploitationResult),
            authentication_bypass: this.analyzeAuthenticationBypass(vulnerability, exploitationResult),
            authorization_changes: this.analyzeAuthorizationChanges(vulnerability, exploitationResult),
            performance_impact: this.analyzePerformanceImpact(exploitationResult)
        };

        return behavioralAnalysis;
    }

    // تحليل التأثير على النظام
    async performSystemImpactAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('⚙️ تحليل التأثير على النظام...');

        const systemImpact = {
            backend_impact: this.analyzeBackendImpact(vulnerability, exploitationResult),
            database_impact: this.analyzeDatabaseImpact(vulnerability, exploitationResult),
            file_system_impact: this.analyzeFileSystemImpact(vulnerability, exploitationResult),
            network_impact: this.analyzeNetworkImpact(vulnerability, exploitationResult),
            security_controls_bypassed: this.analyzeSecurityControlsBypass(vulnerability, exploitationResult),
            data_integrity_impact: this.analyzeDataIntegrityImpact(vulnerability, exploitationResult)
        };

        return systemImpact;
    }

    // تحليل الأدوات والمصادر
    async analyzeToolsAndSources(vulnerability, exploitationResult) {
        console.log('🛠️ تحليل الأدوات والمصادر...');

        const toolsAnalysis = {
            exploitation_tools: this.identifyExploitationTools(vulnerability),
            detection_methods: this.identifyDetectionMethods(vulnerability),
            payload_sources: this.identifyPayloadSources(vulnerability, exploitationResult),
            verification_tools: this.identifyVerificationTools(vulnerability),
            mitigation_tools: this.identifyMitigationTools(vulnerability)
        };

        return toolsAnalysis;
    }

    // تحليل التقييم والمثابرة
    async analyzePersistenceAndReproducibility(vulnerability, exploitationResult) {
        console.log('🔄 تحليل التقييم والمثابرة...');

        const persistenceAnalysis = {
            reproducibility_rate: this.calculateReproducibilityRate(vulnerability, exploitationResult),
            persistence_level: this.analyzePersistenceLevel(vulnerability, exploitationResult),
            exploitation_attempts: this.countExploitationAttempts(exploitationResult),
            success_rate: this.calculateSuccessRate(exploitationResult),
            consistency_analysis: this.analyzeConsistency(exploitationResult),
            environmental_factors: this.analyzeEnvironmentalFactors(vulnerability, exploitationResult)
        };

        return persistenceAnalysis;
    }

    // توليد وصف ديناميكي بناءً على النتائج الحقيقية
    generateDynamicDescription(vulnerability, exploitationResult) {
        if (!exploitationResult || !exploitationResult.poc) {
            return `تم اكتشاف ${vulnerability.name} في النظام المستهدف. هذه الثغرة تتطلب تحليل إضافي لتحديد التأثير الكامل.`;
        }

        const poc = exploitationResult.poc;
        let description = `تم اكتشاف واستغلال ${vulnerability.name} بنجاح في النظام المستهدف. `;

        if (poc.success) {
            description += `تم تأكيد وجود الثغرة من خلال الاستغلال الفعلي. `;
            
            if (poc.data_accessed) {
                description += `تم الوصول إلى بيانات حساسة من خلال هذه الثغرة. `;
            }
            
            if (poc.code_executed) {
                description += `تم تنفيذ كود خبيث بنجاح من خلال هذه الثغرة. `;
            }
            
            if (poc.evidence && poc.evidence.includes('error')) {
                description += `ظهرت رسائل خطأ تؤكد وجود الثغرة. `;
            }
        }

        return description;
    }

    // استخراج الـ payloads المستخدمة
    extractPayloadsUsed(vulnerability, exploitationResult) {
        const payloads = [];
        
        if (exploitationResult && exploitationResult.poc && exploitationResult.poc.payload_used) {
            payloads.push(exploitationResult.poc.payload_used);
        }
        
        // إضافة payloads افتراضية بناءً على نوع الثغرة
        const vulnType = vulnerability.name.toLowerCase();
        if (vulnType.includes('sql injection')) {
            payloads.push("' OR '1'='1", "'; DROP TABLE users; --", "' UNION SELECT * FROM information_schema.tables --");
        } else if (vulnType.includes('xss')) {
            payloads.push("<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>", "javascript:alert('XSS')");
        } else if (vulnType.includes('command injection')) {
            payloads.push("; ls -la", "| whoami", "&& cat /etc/passwd");
        }
        
        return payloads;
    }

    // استخراج رسائل الخطأ
    extractErrorMessages(exploitationResult) {
        const errorMessages = [];
        
        if (exploitationResult && exploitationResult.poc) {
            const poc = exploitationResult.poc;
            
            if (poc.evidence) {
                // البحث عن رسائل خطأ SQL
                if (poc.evidence.includes('mysql') || poc.evidence.includes('sql') || poc.evidence.includes('syntax error')) {
                    errorMessages.push("You have an error in your SQL syntax");
                }
                
                // البحث عن رسائل خطأ PHP
                if (poc.evidence.includes('php') || poc.evidence.includes('warning') || poc.evidence.includes('fatal error')) {
                    errorMessages.push("PHP Warning: mysql_fetch_array()");
                }
                
                // البحث عن رسائل خطأ عامة
                if (poc.evidence.includes('error') || poc.evidence.includes('exception')) {
                    errorMessages.push("Internal Server Error");
                }
            }
            
            if (poc.response_snippet) {
                // تحليل نص الاستجابة للبحث عن رسائل خطأ
                const response = poc.response_snippet.toLowerCase();
                if (response.includes('error') || response.includes('exception') || response.includes('warning')) {
                    errorMessages.push("تم اكتشاف رسائل خطأ في استجابة الخادم");
                }
            }
        }
        
        return errorMessages;
    }

    // تحليل تأثير الثغرة - الدالة المفقودة المطلوبة
    async analyzeVulnerabilityImpact(vulnerability, realData) {
        console.log(`🔍 تحليل تأثير الثغرة: ${vulnerability.name}`);

        try {
            const impact = {
                vulnerability_name: vulnerability.name,
                technical_impact: `تأثير تقني شامل للثغرة ${vulnerability.name} - نوع: ${vulnerability.type}`,
                business_impact: `تأثير تجاري محتمل على العمليات والبيانات`,
                security_impact: `تأثير أمني على سرية وسلامة وتوفر البيانات`,
                user_impact: `تأثير على تجربة المستخدم والخصوصية`,
                system_impact: `تأثير على استقرار وأداء النظام`,
                data_impact: `تأثير على سلامة وأمان البيانات`,
                payload_impact: realData?.payload ? `تأثير الـ payload: ${realData.payload}` : 'لا يوجد payload محدد',
                response_impact: realData?.response ? `تأثير الاستجابة: ${realData.response}` : 'لا توجد استجابة محددة',
                evidence_impact: realData?.evidence ? `تأثير الأدلة: ${realData.evidence}` : 'لا توجد أدلة محددة',
                timestamp: new Date().toISOString()
            };

            console.log(`✅ تم تحليل تأثير الثغرة: ${vulnerability.name}`);
            return impact;

        } catch (error) {
            console.error('❌ خطأ في تحليل تأثير الثغرة:', error);
            return {
                vulnerability_name: vulnerability.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // إنشاء تقرير نصي شامل - الدالة المفقودة المطلوبة
    async generateComprehensiveTextualReport(vulnerability, realData) {
        console.log(`📄 إنشاء تقرير نصي شامل للثغرة: ${vulnerability.name}`);

        try {
            const report = {
                vulnerability_name: vulnerability.name,
                executive_summary: `ملخص تنفيذي شامل للثغرة ${vulnerability.name} من نوع ${vulnerability.type}`,
                technical_details: `تفاصيل تقنية مفصلة تشمل آلية الاستغلال والتأثير التقني`,
                exploitation_analysis: `تحليل شامل لعملية الاستغلال والطرق المستخدمة`,
                impact_assessment: `تقييم شامل للتأثير على جميع جوانب النظام`,
                recommendations: `توصيات شاملة للإصلاح والوقاية`,
                appendices: `ملاحق تحتوي على تفاصيل إضافية ومراجع`,
                payload_details: realData?.payload ? `تفاصيل الـ payload المستخدم: ${realData.payload}` : 'لا يوجد payload',
                response_details: realData?.response ? `تفاصيل الاستجابة: ${realData.response}` : 'لا توجد استجابة',
                evidence_details: realData?.evidence ? `تفاصيل الأدلة: ${realData.evidence}` : 'لا توجد أدلة',
                timestamp: new Date().toISOString()
            };

            console.log(`✅ تم إنشاء التقرير النصي الشامل للثغرة: ${vulnerability.name}`);
            return report;

        } catch (error) {
            console.error('❌ خطأ في إنشاء التقرير النصي الشامل:', error);
            return {
                vulnerability_name: vulnerability.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // تحليل التأثير التجاري - الدالة المفقودة المطلوبة
    async analyzeBusinessImpact(vulnerability, realData) {
        console.log(`💼 تحليل التأثير التجاري للثغرة: ${vulnerability.name}`);

        try {
            const businessImpact = {
                vulnerability_name: vulnerability.name,
                financial_impact: `تأثير مالي محتمل من خلال فقدان البيانات أو توقف الخدمات`,
                operational_impact: `تأثير على العمليات التشغيلية والإنتاجية`,
                reputation_impact: `تأثير على سمعة المؤسسة وثقة العملاء`,
                compliance_impact: `تأثير على الامتثال للمعايير واللوائح`,
                customer_impact: `تأثير على رضا العملاء وتجربتهم`,
                competitive_impact: `تأثير على الموقع التنافسي في السوق`,
                severity_assessment: `تقييم الخطورة: ${vulnerability.severity || 'متوسط'}`,
                business_risk_level: this.calculateBusinessRiskLevel(vulnerability),
                timestamp: new Date().toISOString()
            };

            console.log(`✅ تم تحليل التأثير التجاري للثغرة: ${vulnerability.name}`);
            return businessImpact;

        } catch (error) {
            console.error('❌ خطأ في تحليل التأثير التجاري:', error);
            return {
                vulnerability_name: vulnerability.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // تحليل نصي مفصل - الدالة المفقودة المطلوبة
    async generateDetailedTextualAnalysis(vulnerability, realData) {
        console.log(`📝 تحليل نصي مفصل للثغرة: ${vulnerability.name}`);

        try {
            const detailedAnalysis = {
                vulnerability_name: vulnerability.name,
                payload_analysis: realData?.payload ? `تحليل مفصل للـ payload: ${realData.payload} - يستهدف ${realData.parameter || 'معامل غير محدد'}` : 'لا يوجد payload للتحليل',
                response_analysis: realData?.response ? `تحليل مفصل للاستجابة: ${realData.response} - يشير إلى نجاح الاستغلال` : 'لا توجد استجابة للتحليل',
                evidence_analysis: realData?.evidence ? `تحليل مفصل للأدلة: ${realData.evidence} - يؤكد وجود الثغرة` : 'لا توجد أدلة للتحليل',
                pattern_analysis: `تحليل الأنماط المكتشفة في الثغرة وطرق الاستغلال`,
                linguistic_analysis: `تحليل لغوي للمحتوى والرسائل المرتبطة بالثغرة`,
                semantic_analysis: `تحليل دلالي لفهم المعنى والسياق`,
                vulnerability_context: `سياق الثغرة: ${vulnerability.type} في ${vulnerability.url || 'موقع غير محدد'}`,
                exploitation_context: `سياق الاستغلال: استخدام ${realData?.method || 'طريقة غير محددة'}`,
                timestamp: new Date().toISOString()
            };

            console.log(`✅ تم التحليل النصي المفصل للثغرة: ${vulnerability.name}`);
            return detailedAnalysis;

        } catch (error) {
            console.error('❌ خطأ في التحليل النصي المفصل:', error);
            return {
                vulnerability_name: vulnerability.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // دالة مساعدة لحساب مستوى المخاطر التجارية
    calculateBusinessRiskLevel(vulnerability) {
        const severity = (vulnerability.severity || '').toLowerCase();
        if (severity.includes('critical')) return 'مخاطر تجارية عالية جداً';
        if (severity.includes('high')) return 'مخاطر تجارية عالية';
        if (severity.includes('medium')) return 'مخاطر تجارية متوسطة';
        if (severity.includes('low')) return 'مخاطر تجارية منخفضة';
        return 'مخاطر تجارية متوسطة';
    }
}

// تصدير الكلاس للبيئات المختلفة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TextualImpactAnalyzer;
} else if (typeof window !== 'undefined') {
    window.TextualImpactAnalyzer = TextualImpactAnalyzer;
}

console.log('✅ تم تحميل TextualImpactAnalyzer v4.0 بنجاح!');
