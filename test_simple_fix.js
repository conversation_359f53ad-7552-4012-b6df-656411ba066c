// اختبار بسيط وسريع لإصلاح مشكلة BugBountyCore is not defined
console.log('🔥 اختبار بسيط لإصلاح مشكلة BugBountyCore...');

try {
    console.log('🔍 محاولة تحميل BugBountyCore...');
    
    // محاولة تحميل الملف
    const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
    
    // فحص التصدير
    if (typeof BugBountyCore === 'undefined') {
        console.log('❌ BugBountyCore is not defined - المشكلة لا تزال موجودة!');
        process.exit(1);
    }
    
    if (typeof BugBountyCore !== 'function') {
        console.log(`❌ BugBountyCore ليس دالة - النوع: ${typeof BugBountyCore}`);
        process.exit(1);
    }
    
    console.log('✅ BugBountyCore محدد ومُصدر بشكل صحيح!');
    console.log(`📊 النوع: ${typeof BugBountyCore}`);
    
    // محاولة إنشاء instance بسيط
    try {
        const core = new BugBountyCore();
        console.log('✅ تم إنشاء instance بنجاح!');
        
        // فحص دالة واحدة فقط
        if (typeof core.initializeSystem === 'function') {
            console.log('✅ الدوال الأساسية موجودة');
        } else {
            console.log('⚠️ بعض الدوال قد تكون مفقودة');
        }
        
        console.log('\n🎉 النتيجة النهائية: المشكلة تم حلها بالكامل! ✅');
        console.log('🚀 BugBountyCore يعمل بشكل صحيح');
        
    } catch (constructorError) {
        console.log('❌ خطأ في إنشاء instance:', constructorError.message);
        process.exit(1);
    }
    
} catch (error) {
    console.log('❌ خطأ في تحميل BugBountyCore:', error.message);
    
    if (error.message.includes('BugBountyCore is not defined')) {
        console.log('💥 المشكلة لا تزال موجودة: BugBountyCore is not defined');
    }
    
    process.exit(1);
}

console.log('\n🏁 انتهى الاختبار بنجاح');
