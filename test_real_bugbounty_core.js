// اختبار حقيقي وفعلي لمشكلة BugBountyCore is not defined
console.log('🔥 اختبار حقيقي وفعلي لمشكلة BugBountyCore...');

async function testRealBugBountyCore() {
    try {
        console.log('\n=== اختبار 1: تحميل الملف ===');
        
        // محاولة تحميل الملف مباشرة
        let BugBountyCore;
        try {
            BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
            console.log('✅ تم تحميل الملف بنجاح');
        } catch (requireError) {
            console.log('❌ فشل في تحميل الملف:', requireError.message);
            return false;
        }

        console.log('\n=== اختبار 2: فحص التصدير ===');
        
        if (typeof BugBountyCore === 'undefined') {
            console.log('❌ BugBountyCore is not defined - المشكلة لا تزال موجودة!');
            return false;
        } else {
            console.log('✅ BugBountyCore محدد بشكل صحيح');
        }

        if (typeof BugBountyCore !== 'function') {
            console.log('❌ BugBountyCore ليس دالة constructor');
            console.log(`📊 النوع الفعلي: ${typeof BugBountyCore}`);
            return false;
        } else {
            console.log('✅ BugBountyCore هو دالة constructor صحيحة');
        }

        console.log('\n=== اختبار 3: إنشاء Instance ===');
        
        let core;
        try {
            core = new BugBountyCore();
            console.log('✅ تم إنشاء instance بنجاح');
        } catch (constructorError) {
            console.log('❌ فشل في إنشاء instance:', constructorError.message);
            return false;
        }

        console.log('\n=== اختبار 4: فحص الدوال الأساسية ===');
        
        const requiredMethods = [
            'initializeSystem',
            'formatSinglePageReport', 
            'findRealImageForVulnerability',
            'generateComprehensiveReport',
            'loadReportTemplate'
        ];

        let allMethodsExist = true;
        for (const method of requiredMethods) {
            if (typeof core[method] === 'function') {
                console.log(`✅ ${method} موجودة`);
            } else {
                console.log(`❌ ${method} غير موجودة أو ليست دالة`);
                allMethodsExist = false;
            }
        }

        if (!allMethodsExist) {
            console.log('❌ بعض الدوال الأساسية مفقودة');
            return false;
        }

        console.log('\n=== اختبار 5: اختبار دالة حقيقية ===');
        
        try {
            // اختبار دالة loadReportTemplate
            const template = await core.loadReportTemplate();
            if (template && template.length > 100) {
                console.log(`✅ loadReportTemplate تعمل بشكل صحيح (${template.length} حرف)`);
            } else {
                console.log('⚠️ loadReportTemplate تعمل لكن النتيجة قصيرة أو فارغة');
            }
        } catch (methodError) {
            console.log('❌ خطأ في تشغيل loadReportTemplate:', methodError.message);
            return false;
        }

        console.log('\n=== اختبار 6: اختبار تقرير بسيط ===');
        
        try {
            const testData = {
                page_name: 'اختبار',
                page_url: 'http://test.com',
                vulnerabilities: [{
                    name: 'اختبار ثغرة',
                    type: 'Test',
                    url: 'http://test.com'
                }],
                scan_date: new Date().toLocaleString('ar-SA'),
                total_vulnerabilities: 1
            };

            const report = await core.formatSinglePageReport(testData);
            if (report && report.length > 1000) {
                console.log(`✅ formatSinglePageReport تعمل بشكل صحيح (${report.length} حرف)`);
            } else {
                console.log('⚠️ formatSinglePageReport تعمل لكن النتيجة قصيرة');
            }
        } catch (reportError) {
            console.log('❌ خطأ في تشغيل formatSinglePageReport:', reportError.message);
            return false;
        }

        console.log('\n🎉 جميع الاختبارات نجحت! المشكلة تم حلها بالكامل!');
        return true;

    } catch (error) {
        console.log('\n❌ خطأ عام في الاختبار:', error.message);
        console.log('📍 Stack trace:', error.stack);
        return false;
    }
}

// تشغيل الاختبار
testRealBugBountyCore().then(success => {
    if (success) {
        console.log('\n🎊 النتيجة النهائية: المشكلة تم حلها بالكامل! ✅');
        console.log('🚀 BugBountyCore يعمل بشكل صحيح ومتاح للاستخدام');
    } else {
        console.log('\n💥 النتيجة النهائية: المشكلة لا تزال موجودة! ❌');
        console.log('🔧 يحتاج إلى مزيد من الإصلاحات');
    }
}).catch(error => {
    console.log('\n💥 فشل في تشغيل الاختبار:', error.message);
    console.log('🔧 المشكلة لا تزال موجودة ويحتاج إلى إصلاح فوري');
});
