// Comprehensive Diagnostic Test for BugBountyCore is not defined issue
console.log('='.repeat(60));
console.log('COMPREHENSIVE DIAGNOSTIC TEST FOR BugBountyCore');
console.log('='.repeat(60));

const fs = require('fs');
const path = require('path');

function runDiagnostic() {
    console.log('\n1. CHECKING FILE EXISTENCE...');
    
    const filePath = './assets/modules/bugbounty/BugBountyCore.js';
    const absolutePath = path.resolve(filePath);
    
    if (!fs.existsSync(filePath)) {
        console.log('ERROR: File does not exist at:', filePath);
        console.log('Absolute path:', absolutePath);
        return false;
    }
    
    console.log('SUCCESS: File exists at:', filePath);
    console.log('Absolute path:', absolutePath);
    
    const stats = fs.statSync(filePath);
    console.log('File size:', stats.size, 'bytes');
    console.log('Last modified:', stats.mtime);
    
    console.log('\n2. READING FILE CONTENT...');
    
    let fileContent;
    try {
        fileContent = fs.readFileSync(filePath, 'utf8');
        console.log('SUCCESS: File read successfully');
        console.log('Content length:', fileContent.length, 'characters');
    } catch (readError) {
        console.log('ERROR: Failed to read file:', readError.message);
        return false;
    }
    
    console.log('\n3. ANALYZING FILE STRUCTURE...');
    
    // Check for class definition
    const classMatch = fileContent.match(/class\s+BugBountyCore\s*\{/);
    if (classMatch) {
        console.log('SUCCESS: Found class BugBountyCore definition');
        console.log('Class definition starts at position:', classMatch.index);
    } else {
        console.log('ERROR: No class BugBountyCore definition found');
        return false;
    }
    
    // Check for class closing
    const lines = fileContent.split('\n');
    let classStartLine = -1;
    let classEndLine = -1;
    let braceCount = 0;
    let inClass = false;
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        if (line.includes('class BugBountyCore')) {
            classStartLine = i + 1;
            inClass = true;
            console.log('Class starts at line:', classStartLine);
        }
        
        if (inClass) {
            const openBraces = (line.match(/\{/g) || []).length;
            const closeBraces = (line.match(/\}/g) || []).length;
            braceCount += openBraces - closeBraces;
            
            if (braceCount === 0 && classStartLine !== -1) {
                classEndLine = i + 1;
                console.log('Class ends at line:', classEndLine);
                break;
            }
        }
    }
    
    if (classEndLine === -1) {
        console.log('ERROR: Class BugBountyCore is not properly closed');
        return false;
    }
    
    console.log('SUCCESS: Class BugBountyCore is properly defined from line', classStartLine, 'to', classEndLine);
    
    console.log('\n4. CHECKING EXPORTS...');
    
    // Check for module.exports
    if (fileContent.includes('module.exports = BugBountyCore')) {
        console.log('SUCCESS: Found module.exports = BugBountyCore');
    } else {
        console.log('WARNING: No module.exports = BugBountyCore found');
    }
    
    // Check for window export
    if (fileContent.includes('window.BugBountyCore = BugBountyCore')) {
        console.log('SUCCESS: Found window.BugBountyCore = BugBountyCore');
    } else {
        console.log('WARNING: No window.BugBountyCore = BugBountyCore found');
    }
    
    // Check for global export
    if (fileContent.includes('global.BugBountyCore = BugBountyCore')) {
        console.log('SUCCESS: Found global.BugBountyCore = BugBountyCore');
    } else {
        console.log('WARNING: No global.BugBountyCore = BugBountyCore found');
    }
    
    console.log('\n5. CHECKING FOR SYNTAX ERRORS...');
    
    try {
        // Try to evaluate the code in a safe context
        const vm = require('vm');
        const sandbox = {
            console: console,
            require: require,
            module: { exports: {} },
            exports: {},
            global: {},
            window: {},
            document: undefined,
            setTimeout: setTimeout,
            setInterval: setInterval,
            clearTimeout: clearTimeout,
            clearInterval: clearInterval
        };
        
        vm.createContext(sandbox);
        vm.runInContext(fileContent, sandbox, { timeout: 10000 });
        
        console.log('SUCCESS: No syntax errors found');
        
        // Check if BugBountyCore was defined in sandbox
        if (typeof sandbox.BugBountyCore !== 'undefined') {
            console.log('SUCCESS: BugBountyCore is defined in sandbox');
            console.log('Type:', typeof sandbox.BugBountyCore);
        } else {
            console.log('ERROR: BugBountyCore is not defined in sandbox');
        }
        
        // Check exports
        if (sandbox.module.exports && typeof sandbox.module.exports === 'function') {
            console.log('SUCCESS: module.exports is set to a function');
        } else {
            console.log('ERROR: module.exports is not set correctly');
            console.log('module.exports type:', typeof sandbox.module.exports);
        }
        
    } catch (syntaxError) {
        console.log('ERROR: Syntax error found:', syntaxError.message);
        console.log('Error stack:', syntaxError.stack);
        return false;
    }
    
    console.log('\n6. TESTING ACTUAL REQUIRE...');
    
    try {
        // Clear require cache
        delete require.cache[path.resolve(filePath)];
        
        const BugBountyCore = require(filePath);
        
        if (typeof BugBountyCore === 'undefined') {
            console.log('ERROR: BugBountyCore is undefined after require');
            return false;
        }
        
        if (typeof BugBountyCore !== 'function') {
            console.log('ERROR: BugBountyCore is not a function after require');
            console.log('Actual type:', typeof BugBountyCore);
            console.log('Value:', BugBountyCore);
            return false;
        }
        
        console.log('SUCCESS: BugBountyCore loaded successfully via require');
        console.log('Type:', typeof BugBountyCore);
        
        // Test constructor
        try {
            const instance = new BugBountyCore();
            console.log('SUCCESS: Instance created successfully');
            
            // Test basic methods
            const methods = ['initializeSystem', 'formatSinglePageReport', 'findRealImageForVulnerability'];
            let methodCount = 0;
            
            for (const method of methods) {
                if (typeof instance[method] === 'function') {
                    methodCount++;
                }
            }
            
            console.log('SUCCESS: Found', methodCount, 'out of', methods.length, 'basic methods');
            
        } catch (constructorError) {
            console.log('ERROR: Failed to create instance:', constructorError.message);
            return false;
        }
        
    } catch (requireError) {
        console.log('ERROR: Failed to require BugBountyCore:', requireError.message);
        console.log('Error stack:', requireError.stack);
        return false;
    }
    
    console.log('\n7. FINAL DIAGNOSIS...');
    
    console.log('SUCCESS: All diagnostic tests passed!');
    console.log('BugBountyCore is working correctly in Node.js environment');
    
    return true;
}

// Run the diagnostic
const result = runDiagnostic();

console.log('\n' + '='.repeat(60));
if (result) {
    console.log('FINAL RESULT: BugBountyCore is working correctly!');
    console.log('The issue may be browser-specific or cache-related.');
    console.log('Try hard refresh (Ctrl+Shift+R) in browser.');
} else {
    console.log('FINAL RESULT: Issues found with BugBountyCore!');
    console.log('Check the errors above for specific problems.');
}
console.log('='.repeat(60));
