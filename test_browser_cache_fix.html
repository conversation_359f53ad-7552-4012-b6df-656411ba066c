<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح Cache المتصفح - BugBountyCore</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
            border: 2px solid #f44336;
        }
        .info {
            background: rgba(33, 150, 243, 0.3);
            border: 2px solid #2196F3;
        }
        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: scale(1.05);
        }
        #console {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار إصلاح Cache المتصفح - BugBountyCore</h1>
        
        <div class="test-result info">
            <strong>📅 وقت الاختبار:</strong> <span id="testTime"></span>
        </div>
        
        <div class="test-result info">
            <strong>🌐 Cache Buster:</strong> <span id="cacheBuster"></span>
        </div>
        
        <div id="testResults"></div>
        
        <button onclick="runCacheTest()">🔄 تشغيل اختبار Cache</button>
        <button onclick="clearCache()">🗑️ مسح Cache</button>
        <button onclick="hardRefresh()">⚡ Hard Refresh</button>
        
        <div id="console"></div>
    </div>

    <script>
        // تحديث وقت الاختبار
        document.getElementById('testTime').textContent = new Date().toLocaleString('ar-SA');
        document.getElementById('cacheBuster').textContent = Date.now();
        
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const color = type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3';
            console.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }
        
        function addTestResult(message, success) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `<strong>${success ? '✅' : '❌'}</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function runCacheTest() {
            log('🔥 بدء اختبار Cache المتصفح...', 'info');
            document.getElementById('testResults').innerHTML = '';
            
            try {
                // اختبار 1: تحميل الملف مع cache buster
                log('📥 اختبار 1: تحميل BugBountyCore مع Cache Buster...', 'info');
                
                const cacheBuster = Date.now();
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${cacheBuster}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = () => {
                        log('✅ تم تحميل الملف بنجاح', 'success');
                        resolve();
                    };
                    script.onerror = () => {
                        log('❌ فشل في تحميل الملف', 'error');
                        reject(new Error('فشل في تحميل الملف'));
                    };
                    document.head.appendChild(script);
                });
                
                addTestResult('تحميل الملف مع Cache Buster', true);
                
                // اختبار 2: فحص وجود BugBountyCore
                log('🔍 اختبار 2: فحص وجود BugBountyCore...', 'info');
                
                if (typeof window.BugBountyCore === 'undefined') {
                    log('❌ BugBountyCore غير محدد - المشكلة لا تزال موجودة!', 'error');
                    addTestResult('BugBountyCore محدد في window', false);
                    return;
                } else {
                    log('✅ BugBountyCore محدد بشكل صحيح', 'success');
                    addTestResult('BugBountyCore محدد في window', true);
                }
                
                // اختبار 3: فحص نوع BugBountyCore
                log('🔍 اختبار 3: فحص نوع BugBountyCore...', 'info');
                
                if (typeof window.BugBountyCore !== 'function') {
                    log(`❌ BugBountyCore ليس دالة - النوع: ${typeof window.BugBountyCore}`, 'error');
                    addTestResult('BugBountyCore هو دالة constructor', false);
                    return;
                } else {
                    log('✅ BugBountyCore هو دالة constructor صحيحة', 'success');
                    addTestResult('BugBountyCore هو دالة constructor', true);
                }
                
                // اختبار 4: إنشاء instance
                log('🔍 اختبار 4: إنشاء instance من BugBountyCore...', 'info');
                
                try {
                    const core = new window.BugBountyCore();
                    log('✅ تم إنشاء instance بنجاح', 'success');
                    addTestResult('إنشاء instance من BugBountyCore', true);
                    
                    // اختبار 5: فحص الدوال الأساسية
                    log('🔍 اختبار 5: فحص الدوال الأساسية...', 'info');
                    
                    const requiredMethods = [
                        'initializeSystem',
                        'formatSinglePageReport',
                        'findRealImageForVulnerability'
                    ];
                    
                    let allMethodsExist = true;
                    for (const method of requiredMethods) {
                        if (typeof core[method] === 'function') {
                            log(`✅ ${method} موجودة`, 'success');
                        } else {
                            log(`❌ ${method} غير موجودة`, 'error');
                            allMethodsExist = false;
                        }
                    }
                    
                    addTestResult('جميع الدوال الأساسية موجودة', allMethodsExist);
                    
                    if (allMethodsExist) {
                        log('🎉 جميع الاختبارات نجحت! المشكلة تم حلها بالكامل!', 'success');
                        addTestResult('🎊 النتيجة النهائية: المشكلة حُلت بالكامل!', true);
                    }
                    
                } catch (constructorError) {
                    log(`❌ فشل في إنشاء instance: ${constructorError.message}`, 'error');
                    addTestResult('إنشاء instance من BugBountyCore', false);
                }
                
            } catch (error) {
                log(`❌ خطأ عام في الاختبار: ${error.message}`, 'error');
                addTestResult('الاختبار العام', false);
            }
        }
        
        function clearCache() {
            log('🗑️ محاولة مسح Cache...', 'info');
            
            // مسح localStorage
            localStorage.clear();
            log('✅ تم مسح localStorage', 'success');
            
            // مسح sessionStorage
            sessionStorage.clear();
            log('✅ تم مسح sessionStorage', 'success');
            
            // إعادة تحميل الصفحة مع cache buster
            const url = new URL(window.location);
            url.searchParams.set('cache_buster', Date.now());
            log('🔄 إعادة تحميل الصفحة مع Cache Buster...', 'info');
            
            setTimeout(() => {
                window.location.href = url.toString();
            }, 1000);
        }
        
        function hardRefresh() {
            log('⚡ تنفيذ Hard Refresh...', 'info');
            window.location.reload(true);
        }
        
        // تشغيل الاختبار تلقائياً عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runCacheTest, 1000);
        });
    </script>
</body>
</html>
