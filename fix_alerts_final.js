// Final Fix for Alert Removal - Complete Solution
console.log('🔥 Final Fix for Alert Removal - Complete Solution');
console.log('='.repeat(60));

const fs = require('fs');
const path = require('path');

function fixAlertsInFile() {
    const filePath = './assets/modules/bugbounty/BugBountyCore.js';
    
    console.log('📖 Reading BugBountyCore.js...');
    let content = fs.readFileSync(filePath, 'utf8');
    
    console.log(`📊 Original file size: ${content.length} characters`);
    
    // Count original alerts
    const originalAlerts = (content.match(/alert\(/gi) || []).length;
    console.log(`🔍 Found ${originalAlerts} alert() occurrences`);
    
    // Replace all alert() with console.log() in a comprehensive way
    console.log('🧹 Replacing all alert() with console.log()...');
    
    let fixedContent = content
        // Replace all forms of alert() with console.log()
        .replace(/alert\s*\(\s*["']([^"']*)["']\s*\)/gi, 'console.log("XSS_TEST")')
        .replace(/alert\s*\(\s*([^)]*)\s*\)/gi, 'console.log("XSS_TEST")')
        .replace(/alert\(/gi, 'console.log(')
        
        // Fix specific XSS payloads
        .replace(/<script>alert\([^)]*\)<\/script>/gi, '<script>console.log("XSS_TEST")</script>')
        .replace(/onerror\s*=\s*["']?alert\([^"']*\)["']?/gi, 'onerror="console.log(\\"XSS_TEST\\")"')
        .replace(/onload\s*=\s*["']?alert\([^"']*\)["']?/gi, 'onload="console.log(\\"XSS_TEST\\")"')
        .replace(/onfocus\s*=\s*["']?alert\([^"']*\)["']?/gi, 'onfocus="console.log(\\"XSS_TEST\\")"')
        .replace(/javascript\s*:\s*alert\([^)]*\)/gi, 'javascript:console.log("XSS_TEST")')
        
        // Fix in strings and comments
        .replace(/["']alert\([^"']*\)["']/gi, '"console.log(\\"XSS_TEST\\")"')
        .replace(/`alert\([^`]*\)`/gi, '`console.log("XSS_TEST")`')
        
        // Fix any remaining alert references
        .replace(/\balert\b/gi, 'console.log');
    
    // Count remaining alerts
    const remainingAlerts = (fixedContent.match(/alert\(/gi) || []).length;
    console.log(`✅ Remaining alert() occurrences: ${remainingAlerts}`);
    
    if (remainingAlerts === 0) {
        console.log('🎉 All alerts successfully removed!');
    } else {
        console.log('⚠️ Some alerts may still remain');
    }
    
    console.log(`📊 Fixed file size: ${fixedContent.length} characters`);
    
    // Write the fixed content back
    console.log('💾 Writing fixed content back to file...');
    fs.writeFileSync(filePath, fixedContent, 'utf8');
    
    console.log('✅ File successfully updated!');
    
    return {
        originalAlerts,
        remainingAlerts,
        success: remainingAlerts === 0
    };
}

// Run the fix
try {
    const result = fixAlertsInFile();
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 FINAL RESULTS:');
    console.log(`🔍 Original alerts: ${result.originalAlerts}`);
    console.log(`✅ Remaining alerts: ${result.remainingAlerts}`);
    console.log(`🎯 Success: ${result.success ? 'YES' : 'NO'}`);
    
    if (result.success) {
        console.log('🎉 ALERT REMOVAL COMPLETED SUCCESSFULLY!');
        console.log('✅ No more alert popups will appear in reports');
        console.log('✅ All XSS payloads now use console.log instead');
        console.log('✅ Reports will be clean and professional');
    } else {
        console.log('⚠️ ALERT REMOVAL PARTIALLY COMPLETED');
        console.log('🔍 Some alerts may still remain - manual review needed');
    }
    
    console.log('='.repeat(60));
    
} catch (error) {
    console.error('❌ ERROR during alert removal:', error.message);
    console.error('Stack:', error.stack);
}
