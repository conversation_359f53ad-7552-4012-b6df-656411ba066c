const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

console.log('🚀 اختبار إنتاج تقرير جديد مع الصور المُصلحة...\n');

async function testNewReport() {
    try {
        const bugBounty = new BugBountyCore();
        
        console.log('📋 بدء فحص سريع...');
        
        // تشغيل فحص سريع مع صور
        const result = await bugBounty.startComprehensiveScan('http://testphp.vulnweb.com', {
            maxVulnerabilities: 3, // فحص سريع جداً
            includeScreenshots: true,
            useRealImages: true,
            timeout: 30000 // 30 ثانية فقط
        });
        
        if (result && result.report) {
            console.log('✅ تم إنتاج التقرير بنجاح');
            
            // فحص نوع الصور في التقرير الجديد
            const base64Images = (result.report.match(/data:image/g) || []).length;
            const pathImages = (result.report.match(/\.\/assets\/modules\/bugbounty\/screenshots/g) || []).length;
            const onloadImages = (result.report.match(/onload.*data-base64/g) || []).length;
            
            console.log(`\n📊 إحصائيات الصور في التقرير الجديد:`);
            console.log(`   - صور base64: ${base64Images}`);
            console.log(`   - صور من مسارات: ${pathImages}`);
            console.log(`   - صور مع fallback: ${onloadImages}`);
            
            // حفظ التقرير الجديد
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const reportName = `Bug_Bounty_Fixed_Images_${timestamp}.html`;
            
            require('fs').writeFileSync(reportName, result.report);
            console.log(`\n📄 تم حفظ التقرير الجديد: ${reportName}`);
            
            // فحص عينة من المحتوى
            const sampleContent = result.report.substring(0, 2000);
            if (sampleContent.includes('./assets/modules/bugbounty/screenshots')) {
                console.log('🎉 نجح الإصلاح! التقرير يستخدم مسارات الصور');
            } else {
                console.log('⚠️ التقرير لا يزال يستخدم base64 فقط');
            }
            
            return reportName;
            
        } else {
            console.log('❌ فشل في إنتاج التقرير');
            return null;
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        return null;
    }
}

testNewReport().then(reportName => {
    if (reportName) {
        console.log(`\n🎯 تم الانتهاء! افتح التقرير: ${reportName}`);
    }
});
