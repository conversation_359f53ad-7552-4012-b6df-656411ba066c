<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مباشر - BugBountyCore</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
            padding: 20px;
            margin: 0;
        }
        .result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .info { background: #2196F3; }
        #console {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>🔥 اختبار مباشر - BugBountyCore</h1>
    
    <div id="results"></div>
    <div id="console"></div>

    <script>
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const color = type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3';
            console.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }
        
        function addResult(message, success) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `<strong>${success ? '✅' : '❌'}</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testBugBountyCore() {
            log('🔥 بدء اختبار BugBountyCore المباشر...', 'info');
            
            try {
                // اختبار 1: فحص الحالة الأولية
                log('🔍 اختبار 1: فحص الحالة الأولية...', 'info');
                
                if (typeof window.BugBountyCore !== 'undefined') {
                    log('⚠️ BugBountyCore موجود مسبقاً - مسح الذاكرة...', 'info');
                    delete window.BugBountyCore;
                }
                
                if (typeof window.BugBountyCore === 'undefined') {
                    log('✅ الحالة الأولية صحيحة: BugBountyCore غير محدد', 'success');
                    addResult('الحالة الأولية صحيحة', true);
                } else {
                    log('❌ الحالة الأولية خاطئة: BugBountyCore محدد مسبقاً', 'error');
                    addResult('الحالة الأولية صحيحة', false);
                }
                
                // اختبار 2: تحميل الملف
                log('🔍 اختبار 2: تحميل ملف BugBountyCore.js...', 'info');
                
                const cacheBuster = Date.now();
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${cacheBuster}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = () => {
                        log('✅ تم تحميل الملف بنجاح', 'success');
                        addResult('تحميل الملف', true);
                        resolve();
                    };
                    script.onerror = (error) => {
                        log('❌ فشل في تحميل الملف: ' + error.message, 'error');
                        addResult('تحميل الملف', false);
                        reject(error);
                    };
                    document.head.appendChild(script);
                });
                
                // انتظار قصير للتأكد من تنفيذ الكود
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // اختبار 3: فحص وجود BugBountyCore
                log('🔍 اختبار 3: فحص وجود BugBountyCore...', 'info');
                
                if (typeof window.BugBountyCore === 'undefined') {
                    log('❌ BugBountyCore لا يزال غير محدد بعد التحميل!', 'error');
                    addResult('BugBountyCore محدد', false);
                    
                    // فحص تفصيلي
                    log('🔍 فحص تفصيلي للـ window object...', 'info');
                    const bugKeys = Object.keys(window).filter(k => k.toLowerCase().includes('bug'));
                    log('🔍 المفاتيح المتعلقة بـ Bug: ' + JSON.stringify(bugKeys), 'info');
                    
                    return false;
                } else {
                    log('✅ BugBountyCore محدد بشكل صحيح', 'success');
                    addResult('BugBountyCore محدد', true);
                }
                
                // اختبار 4: فحص النوع
                log('🔍 اختبار 4: فحص نوع BugBountyCore...', 'info');
                
                if (typeof window.BugBountyCore !== 'function') {
                    log(`❌ BugBountyCore ليس دالة - النوع: ${typeof window.BugBountyCore}`, 'error');
                    addResult('BugBountyCore هو دالة', false);
                    return false;
                } else {
                    log('✅ BugBountyCore هو دالة constructor صحيحة', 'success');
                    addResult('BugBountyCore هو دالة', true);
                }
                
                // اختبار 5: إنشاء instance
                log('🔍 اختبار 5: إنشاء instance...', 'info');
                
                try {
                    const core = new window.BugBountyCore();
                    log('✅ تم إنشاء instance بنجاح', 'success');
                    addResult('إنشاء instance', true);
                    
                    // فحص الدوال الأساسية
                    const methods = ['initializeSystem', 'formatSinglePageReport'];
                    let allExist = true;
                    
                    for (const method of methods) {
                        if (typeof core[method] === 'function') {
                            log(`✅ ${method} موجودة`, 'success');
                        } else {
                            log(`❌ ${method} غير موجودة`, 'error');
                            allExist = false;
                        }
                    }
                    
                    addResult('الدوال الأساسية موجودة', allExist);
                    
                    if (allExist) {
                        log('🎉 جميع الاختبارات نجحت! المشكلة تم حلها!', 'success');
                        addResult('🎊 النتيجة النهائية: نجح!', true);
                        return true;
                    }
                    
                } catch (constructorError) {
                    log('❌ فشل في إنشاء instance: ' + constructorError.message, 'error');
                    addResult('إنشاء instance', false);
                    return false;
                }
                
            } catch (error) {
                log('❌ خطأ عام: ' + error.message, 'error');
                addResult('الاختبار العام', false);
                return false;
            }
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(() => {
                testBugBountyCore().then(success => {
                    if (!success) {
                        log('💥 المشكلة لا تزال موجودة!', 'error');
                        addResult('💥 المشكلة لا تزال موجودة!', false);
                    }
                });
            }, 500);
        });
    </script>
</body>
</html>
