const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

console.log('🚀 اختبار التقاط صور الاستغلال الحقيقي...\n');

async function testRealExploitationScreenshots() {
    try {
        const bugBounty = new BugBountyCore();
        
        console.log('📋 إنشاء ثغرة اختبار XSS...');
        
        // إنشاء ثغرة XSS للاختبار
        const testVulnerability = {
            name: 'XSS Reflected Test',
            type: 'XSS',
            severity: 'High',
            description: 'اختبار التقاط صور الاستغلال الحقيقي',
            payload: "<script>alert('XSS Test')</script>",
            target_url: 'http://testphp.vulnweb.com/search.php'
        };
        
        console.log('🎯 اختبار دالة buildExploitedUrl...');
        
        // اختبار دالة بناء URL المُستغل (موجودة في BugBountyCore مباشرة)
        if (bugBounty.buildExploitedUrl) {
            const originalUrl = 'http://testphp.vulnweb.com/search.php';
            const pocResult = {
                payload: "<script>document.body.style.backgroundColor='red';</script>",
                success: true
            };
            
            const exploitedUrl = bugBounty.buildExploitedUrl(originalUrl, testVulnerability, pocResult);
            console.log(`✅ URL الأصلي: ${originalUrl}`);
            console.log(`🎯 URL المُستغل: ${exploitedUrl}`);
            
            // فحص إذا كان URL يحتوي على payload
            if (exploitedUrl.includes('script') && exploitedUrl.includes('backgroundColor')) {
                console.log('🎉 نجح! URL يحتوي على payload حقيقي');
            } else {
                console.log('⚠️ URL لا يحتوي على payload متوقع');
            }
            
            // إنشاء تقرير HTML لاختبار URL
            const testHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>اختبار URL المُستغل</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; }
        .url-box { background: #f8f9fa; padding: 10px; border-radius: 5px; word-break: break-all; }
        iframe { width: 100%; height: 400px; border: 2px solid #28a745; }
    </style>
</head>
<body>
    <h1>🔍 اختبار URL المُستغل</h1>
    
    <div class="test-section">
        <h3>📋 معلومات الاختبار:</h3>
        <p><strong>الثغرة:</strong> ${testVulnerability.name}</p>
        <p><strong>النوع:</strong> ${testVulnerability.type}</p>
        <p><strong>Payload:</strong> ${pocResult.payload}</p>
    </div>
    
    <div class="test-section">
        <h3>🌐 URL الأصلي:</h3>
        <div class="url-box">${originalUrl}</div>
    </div>
    
    <div class="test-section">
        <h3>🎯 URL المُستغل:</h3>
        <div class="url-box">${exploitedUrl}</div>
    </div>
    
    <div class="test-section">
        <h3>🖼️ معاينة الصفحة المُستغلة:</h3>
        <p><strong>تحذير:</strong> هذا اختبار آمن في بيئة محكومة</p>
        <iframe src="${exploitedUrl}" 
                title="معاينة الصفحة المُستغلة"
                sandbox="allow-scripts allow-same-origin"
                onload="console.log('تم تحميل الصفحة المُستغلة')"
                onerror="console.log('فشل تحميل الصفحة المُستغلة')">
        </iframe>
    </div>
    
    <div class="test-section">
        <h3>📊 نتائج الاختبار:</h3>
        <div id="results">⏳ جاري فحص النتائج...</div>
    </div>
    
    <script>
        console.log('🔍 بدء اختبار URL المُستغل...');
        console.log('URL الأصلي:', '${originalUrl}');
        console.log('URL المُستغل:', '${exploitedUrl}');
        
        // فحص إذا كان URL يحتوي على payload
        const exploitedUrl = '${exploitedUrl}';
        const hasPayload = exploitedUrl.includes('script') && exploitedUrl.includes('backgroundColor');
        
        setTimeout(() => {
            const resultsDiv = document.getElementById('results');
            if (hasPayload) {
                resultsDiv.innerHTML = \`
                    <p style="color: green;"><strong>✅ نجح الاختبار!</strong></p>
                    <p>URL يحتوي على payload حقيقي</p>
                    <p>النظام سيلتقط صور للصفحة المُستغلة فعلياً</p>
                \`;
            } else {
                resultsDiv.innerHTML = \`
                    <p style="color: red;"><strong>❌ فشل الاختبار!</strong></p>
                    <p>URL لا يحتوي على payload متوقع</p>
                \`;
            }
        }, 1000);
    </script>
</body>
</html>
            `;
            
            // حفظ ملف الاختبار
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const testFileName = `Real_Exploitation_URL_Test_${timestamp}.html`;
            fs.writeFileSync(testFileName, testHTML);
            
            console.log(`\n📄 تم حفظ ملف اختبار URL: ${testFileName}`);
            console.log('🌐 افتح الملف في المتصفح لرؤية النتائج');
            
            return {
                success: true,
                originalUrl: originalUrl,
                exploitedUrl: exploitedUrl,
                testFile: testFileName,
                hasPayload: exploitedUrl.includes('script')
            };
            
        } else {
            console.log('❌ دالة buildExploitedUrl غير متوفرة في BugBountyCore');
            console.log('🔍 الدوال المتاحة:', Object.getOwnPropertyNames(bugBounty).filter(name => typeof bugBounty[name] === 'function').slice(0, 10));
            return { success: false, error: 'دالة buildExploitedUrl غير متوفرة' };
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        return { success: false, error: error.message };
    }
}

testRealExploitationScreenshots().then(result => {
    console.log('\n📊 نتائج الاختبار:');
    if (result.success) {
        console.log('✅ نجح الاختبار!');
        console.log(`   - URL الأصلي: ${result.originalUrl}`);
        console.log(`   - URL المُستغل: ${result.exploitedUrl}`);
        console.log(`   - يحتوي على payload: ${result.hasPayload ? 'نعم' : 'لا'}`);
        console.log(`   - ملف الاختبار: ${result.testFile}`);
        
        if (result.hasPayload) {
            console.log('\n🎉 الإصلاح نجح! النظام سيلتقط صور للصفحة المُستغلة فعلياً');
            console.log('📸 الصور ستُظهر التغيرات الحقيقية في الصفحة بعد الاستغلال');
        } else {
            console.log('\n⚠️ الإصلاح يحتاج مراجعة - URL لا يحتوي على payload');
        }
    } else {
        console.log('❌ فشل الاختبار:', result.error);
    }
});
