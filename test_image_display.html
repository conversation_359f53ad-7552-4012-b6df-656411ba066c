
<!DOCTYPE html>
<html>
<head>
    <title>اختبار عرض الصور</title>
    <style>
        .image-test { margin: 20px; padding: 15px; border: 1px solid #ddd; }
        img { max-width: 300px; border: 2px solid #28a745; }
    </style>
</head>
<body>
    <h1>اختبار عرض الصور من المجلد</h1>
    
    <div class="image-test">
        <h3>صورة من المجلد:</h3>
        <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/before_XSS.png" 
             alt="صورة اختبار" 
             onerror="console.log('فشل تحميل الصورة من المجلد')">
    </div>
    
    <div class="image-test">
        <h3>صورة كبيرة من المجلد:</h3>
        <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/screenshot_report_1752769591090_bv9ppk0hc_20250717_200028.png" 
             alt="صورة كبيرة" 
             onerror="console.log('فشل تحميل الصورة الكبيرة')">
    </div>
    
    <script>
        console.log('🔍 اختبار تحميل الصور من المجلد...');
        
        // فحص جميع الصور
        document.querySelectorAll('img').forEach((img, index) => {
            img.onload = () => console.log(`✅ تم تحميل الصورة ${index + 1} بنجاح`);
            img.onerror = () => console.log(`❌ فشل تحميل الصورة ${index + 1}`);
        });
    </script>
</body>
</html>
