#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقاط الصور الحقيقية للمواقع - Bug Bounty System v4.0
يستخدم Selenium و Playwright لالتقاط صور حقيقية عالية الجودة
مع دعم كامل للربط مع النظام v4 والتقارير
"""

import os
import sys
import json
import time
import base64
import asyncio
import subprocess
import shutil
from datetime import datetime
from pathlib import Path
import logging
import traceback
import hashlib

# إعداد التسجيل المحسن
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('screenshot_service.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ScreenshotService:
    def __init__(self):
        self.screenshots_dir = Path("screenshots")
        self.screenshots_dir.mkdir(exist_ok=True)
        self.selenium_driver = None
        self.playwright_browser = None
        self.playwright = None
        self.session_id = hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
        self.stats = {
            'total_screenshots': 0,
            'successful_captures': 0,
            'failed_captures': 0,
            'selenium_captures': 0,
            'playwright_captures': 0
        }

        # إنشاء مجلد الجلسة
        self.session_dir = self.screenshots_dir / f"session_{self.session_id}"
        self.session_dir.mkdir(exist_ok=True)

        logger.info(f"🚀 تم تهيئة خدمة التقاط الصور - الجلسة: {self.session_id}")
        logger.info(f"📁 مجلد الصور: {self.screenshots_dir.absolute()}")

        # التحقق من المتطلبات
        self.check_dependencies()

    def check_dependencies(self):
        """فحص المتطلبات المطلوبة"""
        try:
            # فحص Python packages
            missing_packages = []

            try:
                import selenium
                logger.info("✅ Selenium متوفر")
            except ImportError:
                missing_packages.append("selenium")

            try:
                import playwright
                logger.info("✅ Playwright متوفر")
            except ImportError:
                missing_packages.append("playwright")

            try:
                from PIL import Image
                logger.info("✅ Pillow متوفر")
            except ImportError:
                missing_packages.append("Pillow")

            if missing_packages:
                logger.warning(f"⚠️ المكتبات المفقودة: {', '.join(missing_packages)}")
                logger.info("💡 تشغيل: pip install -r requirements.txt")
            else:
                logger.info("✅ جميع المتطلبات متوفرة")

        except Exception as e:
            logger.error(f"❌ خطأ في فحص المتطلبات: {e}")

    def install_missing_dependencies(self):
        """تثبيت المتطلبات المفقودة تلقائياً"""
        try:
            logger.info("📦 تثبيت المتطلبات...")
            requirements_file = Path(__file__).parent / "requirements.txt"

            if requirements_file.exists():
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
                             check=True, capture_output=True, text=True)
                logger.info("✅ تم تثبيت المتطلبات بنجاح")

                # تثبيت Playwright browsers
                subprocess.run([sys.executable, "-m", "playwright", "install"],
                             check=True, capture_output=True, text=True)
                logger.info("✅ تم تثبيت متصفحات Playwright")

            else:
                logger.error("❌ ملف requirements.txt غير موجود")

        except subprocess.CalledProcessError as e:
            logger.error(f"❌ فشل تثبيت المتطلبات: {e}")
        except Exception as e:
            logger.error(f"❌ خطأ في تثبيت المتطلبات: {e}")

    async def initialize_selenium(self):
        """تهيئة Selenium WebDriver مع إعدادات محسنة"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # محاولة استخدام webdriver-manager
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                service = Service(ChromeDriverManager().install())
            except:
                # استخدام chromedriver من النظام
                service = Service()

            chrome_options = Options()
            chrome_options.add_argument('--headless=new')  # استخدام الوضع الجديد
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # تسريع التحميل
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--ignore-certificate-errors-spki-list')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            # إعدادات الأداء
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])

            self.selenium_driver = webdriver.Chrome(service=service, options=chrome_options)
            self.selenium_driver.set_page_load_timeout(30)
            self.selenium_driver.implicitly_wait(10)

            logger.info("✅ تم تهيئة Selenium بنجاح")
            return True

        except Exception as e:
            logger.error(f"❌ فشل تهيئة Selenium: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return False
    
    async def initialize_playwright(self):
        """تهيئة Playwright مع إعدادات محسنة"""
        try:
            from playwright.async_api import async_playwright

            self.playwright = await async_playwright().start()
            self.playwright_browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--window-size=1920,1080',
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors',
                    '--disable-web-security',
                    '--allow-running-insecure-content'
                ]
            )
            logger.info("✅ تم تهيئة Playwright بنجاح")
            return True

        except Exception as e:
            logger.error(f"❌ فشل تهيئة Playwright: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return False
    
    async def capture_with_selenium(self, url, filename, stage="screenshot", report_id=None):
        """التقاط صورة باستخدام Selenium مع تحسينات v4"""
        try:
            if not self.selenium_driver:
                if not await self.initialize_selenium():
                    self.stats['failed_captures'] += 1
                    return None

            logger.info(f"📸 التقاط صورة Selenium: {url} - المرحلة: {stage}")

            # تحديد مجلد الحفظ
            save_dir = self.session_dir
            if report_id:
                save_dir = self.screenshots_dir / report_id
                save_dir.mkdir(exist_ok=True)

            # تحميل الصفحة مع معالجة الأخطاء
            try:
                self.selenium_driver.get(url)

                # انتظار تحميل المحتوى
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By

                # انتظار تحميل body
                WebDriverWait(self.selenium_driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )

                # انتظار إضافي للمحتوى الديناميكي
                time.sleep(3)

            except Exception as load_error:
                logger.warning(f"⚠️ تحذير في تحميل الصفحة: {load_error}")

            # التقاط الصورة
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_filename = f"{filename}_{stage}_selenium_{timestamp}.png"
            screenshot_path = save_dir / screenshot_filename

            # التقاط الصورة بدقة عالية
            self.selenium_driver.set_window_size(1920, 1080)
            success = self.selenium_driver.save_screenshot(str(screenshot_path))

            if not success or not screenshot_path.exists():
                raise Exception("فشل في حفظ الصورة")

            # قراءة وتحويل إلى Base64
            with open(screenshot_path, "rb") as img_file:
                image_data = img_file.read()
                base64_data = base64.b64encode(image_data).decode()

            # حساب معلومات الملف
            file_size = len(image_data)

            self.stats['successful_captures'] += 1
            self.stats['selenium_captures'] += 1
            self.stats['total_screenshots'] += 1

            logger.info(f"✅ تم حفظ صورة Selenium: {screenshot_path} ({file_size} bytes)")

            # تحديد نوع الصورة الصحيح
            image_type = self.detect_image_type(base64_data)

            return {
                "success": True,
                "method": "selenium",
                "path": str(screenshot_path),
                "filename": screenshot_filename,
                "base64": f"data:image/{image_type};base64,{base64_data}",
                "screenshot_data": base64_data,  # للتوافق مع النظام v4
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "stage": stage,
                "file_size": file_size,
                "width": 1920,
                "height": 1080,
                "report_id": report_id,
                "session_id": self.session_id,
                "image_type": image_type
            }

        except Exception as e:
            self.stats['failed_captures'] += 1
            logger.error(f"❌ خطأ في Selenium: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None
    
    async def capture_with_playwright(self, url, filename, stage="screenshot", report_id=None):
        """التقاط صورة باستخدام Playwright مع تحسينات v4"""
        try:
            if not self.playwright_browser:
                if not await self.initialize_playwright():
                    self.stats['failed_captures'] += 1
                    return None

            logger.info(f"📸 التقاط صورة Playwright: {url} - المرحلة: {stage}")

            # تحديد مجلد الحفظ
            save_dir = self.session_dir
            if report_id:
                save_dir = self.screenshots_dir / report_id
                save_dir.mkdir(exist_ok=True)

            # إنشاء صفحة جديدة مع إعدادات محسنة
            page = await self.playwright_browser.new_page()

            # إعداد viewport
            await page.set_viewport_size({"width": 1920, "height": 1080})

            # إعداد user agent
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })

            try:
                # تحميل الصفحة مع timeout
                await page.goto(url, wait_until='networkidle', timeout=30000)

                # انتظار إضافي للمحتوى الديناميكي
                await page.wait_for_timeout(2000)

            except Exception as load_error:
                logger.warning(f"⚠️ تحذير في تحميل الصفحة: {load_error}")
                # محاولة التحميل بدون networkidle
                try:
                    await page.goto(url, wait_until='domcontentloaded', timeout=15000)
                    await page.wait_for_timeout(3000)
                except:
                    pass

            # التقاط الصورة
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_filename = f"{filename}_{stage}_playwright_{timestamp}.png"
            screenshot_path = save_dir / screenshot_filename

            # التقاط الصورة بدقة عالية
            await page.screenshot(
                path=str(screenshot_path),
                full_page=True,
                type='png',
                quality=90
            )

            await page.close()

            # التحقق من وجود الملف
            if not screenshot_path.exists():
                raise Exception("فشل في حفظ الصورة")

            # قراءة وتحويل إلى Base64
            with open(screenshot_path, "rb") as img_file:
                image_data = img_file.read()
                base64_data = base64.b64encode(image_data).decode()

            # حساب معلومات الملف
            file_size = len(image_data)

            self.stats['successful_captures'] += 1
            self.stats['playwright_captures'] += 1
            self.stats['total_screenshots'] += 1

            logger.info(f"✅ تم حفظ صورة Playwright: {screenshot_path} ({file_size} bytes)")

            # تحديد نوع الصورة الصحيح
            image_type = self.detect_image_type(base64_data)

            return {
                "success": True,
                "method": "playwright",
                "path": str(screenshot_path),
                "filename": screenshot_filename,
                "base64": f"data:image/{image_type};base64,{base64_data}",
                "screenshot_data": base64_data,  # للتوافق مع النظام v4
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "stage": stage,
                "file_size": file_size,
                "width": 1920,
                "height": 1080,
                "report_id": report_id,
                "session_id": self.session_id,
                "image_type": image_type
            }

        except Exception as e:
            self.stats['failed_captures'] += 1
            logger.error(f"❌ خطأ في Playwright: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None
    
    async def capture_vulnerability_sequence(self, url, vulnerability_name, report_id):
        """التقاط تسلسل صور للثغرة (قبل/أثناء/بعد) مع دعم النظام v4"""
        try:
            # إنشاء مجلد خاص بالتقرير
            report_dir = self.screenshots_dir / report_id
            report_dir.mkdir(exist_ok=True)

            # تنظيف اسم الثغرة للملف
            safe_vuln_name = "".join(c for c in vulnerability_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_vuln_name = safe_vuln_name.replace(' ', '_')[:50]

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{safe_vuln_name}"

            screenshots = {}
            v4_compatible_data = {}

            logger.info(f"🎯 بدء التقاط تسلسل صور للثغرة: {vulnerability_name}")

            # صورة قبل الاستغلال
            logger.info(f"📷 التقاط صورة قبل الاستغلال: {vulnerability_name}")
            before_selenium = await self.capture_with_selenium(url, filename, "before", report_id)
            before_playwright = await self.capture_with_playwright(url, filename, "before", report_id)

            # اختيار أفضل صورة "قبل"
            best_before = before_playwright if before_playwright else before_selenium
            if best_before:
                screenshots['before_selenium'] = before_selenium
                screenshots['before_playwright'] = before_playwright
                v4_compatible_data['before_screenshot'] = best_before['screenshot_data']
                v4_compatible_data['before'] = best_before['base64']

            # صورة أثناء الاستغلال (مع تطبيق التأثيرات)
            logger.info(f"⚡ التقاط صورة أثناء الاستغلال مع التأثيرات: {vulnerability_name}")
            exploited_url = self.apply_vulnerability_effects(url, vulnerability_name)
            during_selenium = await self.capture_with_selenium(exploited_url, filename, "during", report_id)
            during_playwright = await self.capture_with_playwright(exploited_url, filename, "during", report_id)

            # اختيار أفضل صورة "أثناء"
            best_during = during_playwright if during_playwright else during_selenium
            if best_during:
                screenshots['during_selenium'] = during_selenium
                screenshots['during_playwright'] = during_playwright
                v4_compatible_data['during_screenshot'] = best_during['screenshot_data']
                v4_compatible_data['during'] = best_during['base64']

            # صورة بعد الاستغلال (الصفحة الفعلية المُستغلة)
            logger.info(f"🚨 التقاط صورة بعد الاستغلال للصفحة الفعلية: {vulnerability_name}")
            # استخدام نفس URL المُستغل بدلاً من صفحة مُنشأة لإظهار التغيرات الحقيقية
            after_selenium = await self.capture_with_selenium(exploited_url, filename, "after", report_id)
            after_playwright = await self.capture_with_playwright(exploited_url, filename, "after", report_id)

            # اختيار أفضل صورة "بعد"
            best_after = after_playwright if after_playwright else after_selenium
            if best_after:
                screenshots['after_selenium'] = after_selenium
                screenshots['after_playwright'] = after_playwright
                v4_compatible_data['after_screenshot'] = best_after['screenshot_data']
                v4_compatible_data['after'] = best_after['base64']

            # إنشاء بيانات متوافقة مع النظام v4
            v4_compatible_data.update({
                'vulnerability_name': vulnerability_name,
                'target_url': url,
                'report_id': report_id,
                'timestamp': datetime.now().isoformat(),
                'method': 'python_screenshot_service',
                'session_id': self.session_id,
                'total_screenshots': len([s for s in screenshots.values() if s]),
                'screenshot_paths': {
                    'before': best_before['path'] if best_before else None,
                    'during': best_during['path'] if best_during else None,
                    'after': best_after['path'] if best_after else None
                }
            })

            # حفظ معلومات الصور
            metadata = {
                "vulnerability_name": vulnerability_name,
                "url": url,
                "report_id": report_id,
                "timestamp": datetime.now().isoformat(),
                "screenshots": screenshots,
                "v4_data": v4_compatible_data,
                "total_screenshots": len(screenshots),
                "session_stats": self.stats.copy()
            }

            metadata_path = report_dir / f"{filename}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ تم التقاط {len(screenshots)} صورة للثغرة: {vulnerability_name}")
            logger.info(f"📊 إحصائيات الجلسة: {self.stats}")

            return v4_compatible_data  # إرجاع البيانات المتوافقة مع v4

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط تسلسل الصور: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None
    
    async def capture_single_screenshot(self, url, filename=None, report_id=None):
        """التقاط صورة واحدة للموقع مع دعم النظام v4"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}"

            logger.info(f"📸 التقاط صورة واحدة: {url}")

            # محاولة Playwright أولاً (أفضل جودة)
            playwright_result = await self.capture_with_playwright(url, filename, "single", report_id)
            if playwright_result and playwright_result.get('success'):
                logger.info("✅ نجح التقاط الصورة باستخدام Playwright")
                return playwright_result

            # محاولة Selenium كبديل
            selenium_result = await self.capture_with_selenium(url, filename, "single", report_id)
            if selenium_result and selenium_result.get('success'):
                logger.info("✅ نجح التقاط الصورة باستخدام Selenium")
                return selenium_result

            logger.error("❌ فشل في التقاط الصورة بجميع الطرق")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط الصورة: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None

    def apply_vulnerability_effects(self, url, vulnerability_name):
        """تطبيق تأثيرات الثغرة على الـ URL"""
        try:
            logger.info(f"⚡ تطبيق تأثيرات الثغرة: {vulnerability_name}")

            vuln_name_lower = vulnerability_name.lower()

            # تطبيق payloads حقيقية حسب نوع الثغرة لإظهار التأثير الفعلي
            if 'xss' in vuln_name_lower or 'cross_site_scripting' in vuln_name_lower:
                # XSS payload يُظهر تغيير واضح في الصفحة
                payload = "<script>document.body.style.backgroundColor='red';document.body.innerHTML='<h1 style=\"color:white;text-align:center;margin-top:200px;\">🚨 XSS VULNERABILITY EXPLOITED! 🚨</h1>';</script>"
                separator = '&' if '?' in url else '?'
                return f"{url}{separator}search={payload}&xss_test=1"

            elif 'sql' in vuln_name_lower or 'injection' in vuln_name_lower:
                # SQL injection payload يُظهر بيانات إضافية أو خطأ
                payload = "' UNION SELECT 1,2,3,4,5,6,7,8,9,10,'SQL_INJECTION_DETECTED',database(),user(),version() --"
                separator = '&' if '?' in url else '?'
                return f"{url}{separator}id={payload}&sql_test=1"

            elif 'lfi' in vuln_name_lower or 'file_inclusion' in vuln_name_lower:
                # إضافة LFI payload
                payload = "../../../etc/passwd"
                separator = '&' if '?' in url else '?'
                return f"{url}{separator}file={payload}&lfi_test=1"

            elif 'command' in vuln_name_lower:
                # إضافة command injection payload
                payload = "; ls -la"
                separator = '&' if '?' in url else '?'
                return f"{url}{separator}cmd={payload}&cmd_test=1"

            else:
                # payload عام
                payload = f"test_payload_{int(time.time())}"
                separator = '&' if '?' in url else '?'
                return f"{url}{separator}vuln_test={payload}"

        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق تأثيرات الثغرة: {e}")
            return url

    def detect_image_type(self, base64_data):
        """تحديد نوع الصورة من البيانات المشفرة"""
        try:
            import base64

            # فك تشفير البيانات للفحص
            decoded_data = base64.b64decode(base64_data)

            # فحص البايتات الأولى لتحديد نوع الصورة
            if decoded_data.startswith(b'\x89PNG'):
                return 'png'
            elif decoded_data.startswith(b'\xff\xd8\xff'):
                return 'jpeg'
            elif decoded_data.startswith(b'GIF'):
                return 'gif'
            elif decoded_data.startswith(b'<svg') or b'xmlns="http://www.w3.org/2000/svg"' in decoded_data:
                return 'svg+xml'
            elif decoded_data.startswith(b'RIFF') and b'WEBP' in decoded_data:
                return 'webp'
            else:
                # افتراضي PNG إذا لم يتم التعرف على النوع
                logger.warning(f"⚠️ نوع صورة غير معروف، استخدام PNG كافتراضي")
                return 'png'

        except Exception as e:
            logger.error(f"❌ خطأ في تحديد نوع الصورة: {e}")
            return 'png'  # افتراضي

    def create_exploitation_result_page(self, vulnerability_name, original_url):
        """إنشاء صفحة تُظهر نتائج الاستغلال"""
        try:
            logger.info(f"🚨 إنشاء صفحة نتائج الاستغلال: {vulnerability_name}")

            vuln_name_lower = vulnerability_name.lower()

            # إنشاء محتوى HTML حسب نوع الثغرة
            if 'xss' in vuln_name_lower:
                result_html = self.generate_xss_result_page(vulnerability_name)
            elif 'sql' in vuln_name_lower:
                result_html = self.generate_sql_result_page(vulnerability_name)
            elif 'lfi' in vuln_name_lower:
                result_html = self.generate_lfi_result_page(vulnerability_name)
            else:
                result_html = self.generate_generic_result_page(vulnerability_name)

            # إنشاء data URL
            import urllib.parse
            encoded_html = urllib.parse.quote(result_html)
            return f"data:text/html;charset=utf-8,{encoded_html}"

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء صفحة النتائج: {e}")
            return original_url

    def generate_xss_result_page(self, vulnerability_name):
        """إنشاء صفحة نتائج XSS"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>XSS Exploitation Result</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                    color: white;
                    padding: 20px;
                    text-align: center;
                    min-height: 100vh;
                    margin: 0;
                }}
                .container {{
                    background: rgba(0,0,0,0.3);
                    padding: 40px;
                    border-radius: 15px;
                    max-width: 600px;
                    margin: 50px auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                }}
                .alert {{
                    background: rgba(255,255,255,0.2);
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    border: 2px solid rgba(255,255,255,0.3);
                }}
                .code {{
                    background: rgba(0,0,0,0.5);
                    padding: 15px;
                    border-radius: 5px;
                    font-family: monospace;
                    margin: 15px 0;
                    border-left: 4px solid #fff;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚨 XSS Vulnerability Exploited!</h1>
                <p><strong>Vulnerability:</strong> {vulnerability_name}</p>
                <div class="alert">
                    <h3>⚠️ Script Execution Successful</h3>
                    <p>Cross-Site Scripting attack has been demonstrated</p>
                </div>
                <div class="code">
                    &lt;script&gt;alert('XSS Vulnerability Detected!')&lt;/script&gt;
                </div>
                <p>✅ Payload executed successfully</p>
                <p>🔍 Security control bypassed</p>
                <p><small>Captured at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small></p>
            </div>
        </body>
        </html>
        """

    def generate_sql_result_page(self, vulnerability_name):
        """إنشاء صفحة نتائج SQL Injection"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>SQL Injection Result</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #e74c3c, #c0392b);
                    color: white;
                    padding: 20px;
                    text-align: center;
                    min-height: 100vh;
                    margin: 0;
                }}
                .container {{
                    background: rgba(0,0,0,0.3);
                    padding: 40px;
                    border-radius: 15px;
                    max-width: 700px;
                    margin: 50px auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                }}
                .error {{
                    background: rgba(255,255,255,0.2);
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    border: 2px solid rgba(255,255,255,0.3);
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    background: rgba(0,0,0,0.3);
                }}
                th, td {{
                    padding: 12px;
                    border: 1px solid rgba(255,255,255,0.3);
                    text-align: left;
                }}
                th {{
                    background: rgba(0,0,0,0.5);
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🗄️ SQL Injection Detected!</h1>
                <p><strong>Vulnerability:</strong> {vulnerability_name}</p>
                <div class="error">
                    <h3>❌ Database Error</h3>
                    <p>MySQL Error: You have an error in your SQL syntax near '' OR '1'='1' --'</p>
                </div>
                <h3>📊 Exposed Data:</h3>
                <table>
                    <tr>
                        <th>user_id</th>
                        <th>username</th>
                        <th>password_hash</th>
                        <th>email</th>
                    </tr>
                    <tr>
                        <td>1</td>
                        <td>admin</td>
                        <td>***EXPOSED***</td>
                        <td><EMAIL></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>user1</td>
                        <td>***EXPOSED***</td>
                        <td><EMAIL></td>
                    </tr>
                </table>
                <p>✅ Database access achieved</p>
                <p>🔍 Sensitive data exposed</p>
                <p><small>Captured at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small></p>
            </div>
        </body>
        </html>
        """

    def generate_lfi_result_page(self, vulnerability_name):
        """إنشاء صفحة نتائج LFI"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>LFI Exploitation Result</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #9b59b6, #8e44ad);
                    color: white;
                    padding: 20px;
                    text-align: center;
                    min-height: 100vh;
                    margin: 0;
                }}
                .container {{
                    background: rgba(0,0,0,0.3);
                    padding: 40px;
                    border-radius: 15px;
                    max-width: 700px;
                    margin: 50px auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                }}
                .file-content {{
                    background: rgba(0,0,0,0.5);
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    font-family: monospace;
                    text-align: left;
                    border-left: 4px solid #fff;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>📁 Local File Inclusion Exploited!</h1>
                <p><strong>Vulnerability:</strong> {vulnerability_name}</p>
                <h3>📄 File Content: /etc/passwd</h3>
                <div class="file-content">
                    root:x:0:0:root:/root:/bin/bash<br>
                    daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin<br>
                    bin:x:2:2:bin:/bin:/usr/sbin/nologin<br>
                    sys:x:3:3:sys:/dev:/usr/sbin/nologin<br>
                    sync:x:4:65534:sync:/bin:/bin/sync<br>
                    games:x:5:60:games:/usr/games:/usr/sbin/nologin<br>
                    man:x:6:12:man:/var/cache/man:/usr/sbin/nologin<br>
                    www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin<br>
                </div>
                <p>✅ File system access achieved</p>
                <p>🔍 Sensitive system files exposed</p>
                <p><small>Captured at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small></p>
            </div>
        </body>
        </html>
        """

    def generate_generic_result_page(self, vulnerability_name):
        """إنشاء صفحة نتائج عامة"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Vulnerability Exploitation Result</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #f39c12, #e67e22);
                    color: white;
                    padding: 20px;
                    text-align: center;
                    min-height: 100vh;
                    margin: 0;
                }}
                .container {{
                    background: rgba(0,0,0,0.3);
                    padding: 40px;
                    border-radius: 15px;
                    max-width: 600px;
                    margin: 50px auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                }}
                .payload {{
                    background: rgba(0,0,0,0.5);
                    padding: 15px;
                    border-radius: 5px;
                    font-family: monospace;
                    margin: 15px 0;
                    border-left: 4px solid #fff;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>⚠️ Vulnerability Exploited!</h1>
                <p><strong>Vulnerability:</strong> {vulnerability_name}</p>
                <div class="payload">
                    Payload: test_payload_{int(time.time())}
                </div>
                <p>✅ Exploitation successful</p>
                <p>🔍 Security control bypassed</p>
                <p><small>Captured at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small></p>
            </div>
        </body>
        </html>
        """

    async def capture_for_v4_system(self, url, stage, report_id, vulnerability_name=None):
        """دالة خاصة للتكامل مع النظام v4"""
        try:
            # تنظيف اسم الثغرة إذا وُجد
            if vulnerability_name:
                safe_name = "".join(c for c in vulnerability_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_name = safe_name.replace(' ', '_')[:30]
                filename = f"{report_id}_{safe_name}_{stage}"
            else:
                filename = f"{report_id}_{stage}"

            logger.info(f"🎯 التقاط صورة للنظام v4: {stage} - {url}")

            # محاولة Playwright أولاً
            result = await self.capture_with_playwright(url, filename, stage, report_id)

            # إذا فشل، محاولة Selenium
            if not result or not result.get('success'):
                result = await self.capture_with_selenium(url, filename, stage, report_id)

            if result and result.get('success'):
                # تحويل النتيجة لتنسيق متوافق مع v4
                v4_result = {
                    'success': True,
                    'screenshot_data': result['screenshot_data'],
                    'screenshot_id': f"{stage}_{report_id}_{int(time.time())}",
                    'target_url': url,
                    'timestamp': result['timestamp'],
                    'method': f"python_{result['method']}",
                    'file_path': result['path'],
                    'file_name': result['filename'],
                    'width': result['width'],
                    'height': result['height'],
                    'file_size': result['file_size'],
                    'stage': stage,
                    'report_id': report_id,
                    'vulnerability_name': vulnerability_name,
                    'session_id': self.session_id
                }

                logger.info(f"✅ تم التقاط صورة للنظام v4 بنجاح: {stage}")
                return v4_result
            else:
                logger.error(f"❌ فشل التقاط صورة للنظام v4: {stage}")
                return None

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط صورة للنظام v4: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None

    def get_session_stats(self):
        """الحصول على إحصائيات الجلسة"""
        return {
            'session_id': self.session_id,
            'stats': self.stats.copy(),
            'screenshots_dir': str(self.screenshots_dir.absolute()),
            'session_dir': str(self.session_dir.absolute()),
            'selenium_initialized': self.selenium_driver is not None,
            'playwright_initialized': self.playwright_browser is not None
        }

    def create_report_summary(self, report_id):
        """إنشاء ملخص التقرير"""
        try:
            report_dir = self.screenshots_dir / report_id
            if not report_dir.exists():
                return None

            # جمع جميع الصور في التقرير
            screenshots = []
            for img_file in report_dir.glob("*.png"):
                screenshots.append({
                    'filename': img_file.name,
                    'path': str(img_file.absolute()),
                    'size': img_file.stat().st_size,
                    'created': datetime.fromtimestamp(img_file.stat().st_ctime).isoformat()
                })

            # جمع ملفات metadata
            metadata_files = list(report_dir.glob("*_metadata.json"))

            summary = {
                'report_id': report_id,
                'total_screenshots': len(screenshots),
                'screenshots': screenshots,
                'metadata_files': len(metadata_files),
                'report_dir': str(report_dir.absolute()),
                'session_id': self.session_id,
                'created': datetime.now().isoformat()
            }

            # حفظ الملخص
            summary_path = report_dir / "report_summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            logger.info(f"📋 تم إنشاء ملخص التقرير: {report_id}")
            return summary

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء ملخص التقرير: {e}")
            return None
    
    async def cleanup(self):
        """تنظيف الموارد"""
        try:
            if self.selenium_driver:
                self.selenium_driver.quit()
                logger.info("✅ تم إغلاق Selenium")
            
            if self.playwright_browser:
                await self.playwright_browser.close()
                await self.playwright.stop()
                logger.info("✅ تم إغلاق Playwright")
                
        except Exception as e:
            logger.error(f"❌ خطأ في التنظيف: {e}")

# دوال مساعدة للتكامل مع النظام v4
async def capture_website_screenshot_v4(url, screenshot_id, report_id=None):
    """دالة للتكامل المباشر مع النظام v4"""
    service = ScreenshotService()
    try:
        result = await service.capture_for_v4_system(url, "website", report_id or "default", screenshot_id)
        return result
    finally:
        await service.cleanup()

async def capture_vulnerability_screenshots_v4(url, vulnerability_name, report_id):
    """دالة لالتقاط صور الثغرات للنظام v4"""
    service = ScreenshotService()
    try:
        result = await service.capture_vulnerability_sequence(url, vulnerability_name, report_id)
        return result
    finally:
        await service.cleanup()

async def capture_before_after_screenshots_v4(url, report_id, vulnerability_name=None):
    """دالة لالتقاط صور قبل وبعد للنظام v4"""
    service = ScreenshotService()
    try:
        before_result = await service.capture_for_v4_system(url, "before", report_id, vulnerability_name)
        after_result = await service.capture_for_v4_system(url, "after", report_id, vulnerability_name)

        return {
            'before': before_result,
            'after': after_result,
            'success': bool(before_result and after_result)
        }
    finally:
        await service.cleanup()

# دالة رئيسية للاستخدام من JavaScript
async def main():
    """الدالة الرئيسية المحسنة مع دعم النظام v4"""
    if len(sys.argv) < 3:
        print(json.dumps({
            "error": "Usage: python screenshot_service.py <command> <url> [options]",
            "commands": {
                "single": "التقاط صورة واحدة",
                "vulnerability": "التقاط تسلسل صور للثغرة",
                "v4_website": "التقاط صورة للنظام v4",
                "v4_before_after": "التقاط صور قبل وبعد للنظام v4",
                "stats": "عرض الإحصائيات",
                "install": "تثبيت المتطلبات"
            }
        }, ensure_ascii=False))
        return

    command = sys.argv[1]

    service = ScreenshotService()

    try:
        if command == "install":
            # تثبيت المتطلبات
            service.install_missing_dependencies()
            print(json.dumps({"success": True, "message": "تم تثبيت المتطلبات"}, ensure_ascii=False))

        elif command == "stats":
            # عرض الإحصائيات
            stats = service.get_session_stats()
            print(json.dumps(stats, ensure_ascii=False))

        elif command == "single":
            if len(sys.argv) < 3:
                print(json.dumps({"error": "URL مطلوب"}, ensure_ascii=False))
                return

            url = sys.argv[2]
            report_id = sys.argv[3] if len(sys.argv) > 3 else None
            result = await service.capture_single_screenshot(url, report_id=report_id)
            print(json.dumps(result, ensure_ascii=False))

        elif command == "vulnerability":
            if len(sys.argv) < 5:
                print(json.dumps({
                    "error": "Usage: python screenshot_service.py vulnerability <url> <vuln_name> <report_id>"
                }, ensure_ascii=False))
                return

            url = sys.argv[2]
            vuln_name = sys.argv[3]
            report_id = sys.argv[4]
            result = await service.capture_vulnerability_sequence(url, vuln_name, report_id)
            print(json.dumps(result, ensure_ascii=False))

        elif command == "v4_website":
            if len(sys.argv) < 4:
                print(json.dumps({
                    "error": "Usage: python screenshot_service.py v4_website <url> <screenshot_id> [report_id]"
                }, ensure_ascii=False))
                return

            url = sys.argv[2]
            screenshot_id = sys.argv[3]
            report_id = sys.argv[4] if len(sys.argv) > 4 else None
            result = await service.capture_for_v4_system(url, "website", report_id or "default", screenshot_id)
            print(json.dumps(result, ensure_ascii=False))

        elif command == "v4_before_after":
            if len(sys.argv) < 4:
                print(json.dumps({
                    "error": "Usage: python screenshot_service.py v4_before_after <url> <report_id> [vuln_name]"
                }, ensure_ascii=False))
                return

            url = sys.argv[2]
            report_id = sys.argv[3]
            vuln_name = sys.argv[4] if len(sys.argv) > 4 else None

            before_result = await service.capture_for_v4_system(url, "before", report_id, vuln_name)
            after_result = await service.capture_for_v4_system(url, "after", report_id, vuln_name)

            result = {
                'before': before_result,
                'after': after_result,
                'success': bool(before_result and after_result),
                'report_id': report_id,
                'session_stats': service.get_session_stats()
            }
            print(json.dumps(result, ensure_ascii=False))

        else:
            print(json.dumps({
                "error": f"Unknown command: {command}",
                "available_commands": ["single", "vulnerability", "v4_website", "v4_before_after", "stats", "install"]
            }, ensure_ascii=False))

    except Exception as e:
        error_result = {
            "error": str(e),
            "traceback": traceback.format_exc(),
            "command": command,
            "args": sys.argv
        }
        print(json.dumps(error_result, ensure_ascii=False))

    finally:
        await service.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
