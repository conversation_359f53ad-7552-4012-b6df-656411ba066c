const fs = require('fs');

console.log('🔍 فحص محتوى الدوال الـ36 الفعلي...\n');

try {
    const reportContent = fs.readFileSync('Bug_Bounty_Page_1_testphp_vulnweb_com_20250717T154335.html', 'utf8');
    
    // البحث عن محتوى دالة محددة
    console.log('🔍 البحث عن محتوى Function 1:');
    const function1Match = reportContent.match(/Function 1:.*?(?=Function 2:|$)/s);
    if (function1Match) {
        const content = function1Match[0].substring(0, 500);
        console.log('✅ محتوى Function 1 موجود:');
        console.log(content + '...\n');
    } else {
        console.log('❌ محتوى Function 1 غير موجود\n');
    }
    
    // البحث عن تفاصيل الثغرات الديناميكية
    console.log('🔍 البحث عن تفاصيل الثغرات الديناميكية:');
    const vulnDetailsMatch = reportContent.match(/تم اكتشاف ثغرة.*?في.*?testphp\.vulnweb\.com/g);
    if (vulnDetailsMatch) {
        console.log(`✅ وجدت ${vulnDetailsMatch.length} تفصيل ثغرة ديناميكي`);
        console.log('عينة:', vulnDetailsMatch[0]);
    } else {
        console.log('❌ لا توجد تفاصيل ثغرات ديناميكية');
    }
    
    // البحث عن تفاصيل الاستغلال
    console.log('\n🔍 البحث عن تفاصيل الاستغلال:');
    const exploitMatch = reportContent.match(/خطوات الاستغلال.*?(?=<\/|$)/s) ||
                        reportContent.match(/generateRealExploitationSteps.*?(?=<\/|$)/s) ||
                        reportContent.match(/Exploitation Steps.*?(?=<\/|$)/s);
    if (exploitMatch) {
        console.log('✅ تفاصيل الاستغلال موجودة:');
        console.log(exploitMatch[0].substring(0, 300) + '...');

        // عد عدد مرات ظهور خطوات الاستغلال
        const exploitCount = (reportContent.match(/خطوات الاستغلال|generateRealExploitationSteps/g) || []).length;
        console.log(`📊 عدد مرات ظهور تفاصيل الاستغلال: ${exploitCount}`);
    } else {
        console.log('❌ تفاصيل الاستغلال غير موجودة');

        // البحث عن أي نص يحتوي على "استغلال"
        const anyExploitMatch = reportContent.match(/استغلال/g);
        if (anyExploitMatch) {
            console.log(`⚠️ وجدت ${anyExploitMatch.length} مرة لكلمة "استغلال" لكن ليس بالتنسيق المطلوب`);
        }
    }
    
    // البحث عن تفاصيل التأثير
    console.log('\n🔍 البحث عن تفاصيل التأثير:');
    const impactMatch = reportContent.match(/تأثير.*?الثغرة.*?(?=<\/|$)/s);
    if (impactMatch) {
        console.log('✅ تفاصيل التأثير موجودة:');
        console.log(impactMatch[0].substring(0, 300) + '...');
    } else {
        console.log('❌ تفاصيل التأثير غير موجودة');
    }

    // فحص إضافي شامل للمحتوى
    console.log('\n🔍 فحص شامل إضافي:');

    // البحث عن محتوى الثغرات المفصل
    const vulnDetailsCount = (reportContent.match(/API Authentication Bypass|XSS|SQL Injection|IDOR|CORS/g) || []).length;
    console.log(`🎯 عدد الثغرات المفصلة: ${vulnDetailsCount}`);

    // البحث عن الدوال الشاملة
    const comprehensiveFunctions = (reportContent.match(/generateComprehensive|generateReal|generateAdvanced/g) || []).length;
    console.log(`⚙️ عدد الدوال الشاملة: ${comprehensiveFunctions}`);

    // البحث عن التفاصيل التقنية
    const technicalDetails = (reportContent.match(/تفاصيل التنفيذ|أمثلة الاستخدام|الوصف التفصيلي/g) || []).length;
    console.log(`🔧 عدد التفاصيل التقنية: ${technicalDetails}`);

    // البحث عن محتوى الاختبار الحقيقي
    const realTestingContent = (reportContent.match(/اختبار حقيقي|تم اكتشاف|تم تأكيد/g) || []).length;
    console.log(`🧪 عدد محتوى الاختبار الحقيقي: ${realTestingContent}`);

    console.log('\n📊 ملخص التقييم النهائي:');
    if (vulnDetailsCount > 1000 && comprehensiveFunctions > 500 && technicalDetails > 1000) {
        console.log('🎉 التقرير يحتوي على محتوى شامل وتفصيلي ممتاز!');
    } else {
        console.log('⚠️ التقرير يحتاج لمزيد من المحتوى التفصيلي');
    }
    
} catch (error) {
    console.error('❌ خطأ في قراءة التقرير:', error.message);
}
