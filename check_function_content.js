const fs = require('fs');

console.log('🔍 فحص محتوى الدوال الـ36 الفعلي...\n');

try {
    const reportContent = fs.readFileSync('Bug_Bounty_Page_1_testphp_vulnweb_com_20250717T154335.html', 'utf8');
    
    // البحث عن محتوى دالة محددة
    console.log('🔍 البحث عن محتوى Function 1:');
    const function1Match = reportContent.match(/Function 1:.*?(?=Function 2:|$)/s);
    if (function1Match) {
        const content = function1Match[0].substring(0, 500);
        console.log('✅ محتوى Function 1 موجود:');
        console.log(content + '...\n');
    } else {
        console.log('❌ محتوى Function 1 غير موجود\n');
    }
    
    // البحث عن تفاصيل الثغرات الديناميكية
    console.log('🔍 البحث عن تفاصيل الثغرات الديناميكية:');
    const vulnDetailsMatch = reportContent.match(/تم اكتشاف ثغرة.*?في.*?testphp\.vulnweb\.com/g);
    if (vulnDetailsMatch) {
        console.log(`✅ وجدت ${vulnDetailsMatch.length} تفصيل ثغرة ديناميكي`);
        console.log('عينة:', vulnDetailsMatch[0]);
    } else {
        console.log('❌ لا توجد تفاصيل ثغرات ديناميكية');
    }
    
    // البحث عن تفاصيل الاستغلال
    console.log('\n🔍 البحث عن تفاصيل الاستغلال:');
    const exploitMatch = reportContent.match(/طريقة الاستغلال.*?(?=<\/|$)/s);
    if (exploitMatch) {
        console.log('✅ تفاصيل الاستغلال موجودة:');
        console.log(exploitMatch[0].substring(0, 300) + '...');
    } else {
        console.log('❌ تفاصيل الاستغلال غير موجودة');
    }
    
    // البحث عن تفاصيل التأثير
    console.log('\n🔍 البحث عن تفاصيل التأثير:');
    const impactMatch = reportContent.match(/تأثير.*?الثغرة.*?(?=<\/|$)/s);
    if (impactMatch) {
        console.log('✅ تفاصيل التأثير موجودة:');
        console.log(impactMatch[0].substring(0, 300) + '...');
    } else {
        console.log('❌ تفاصيل التأثير غير موجودة');
    }
    
} catch (error) {
    console.error('❌ خطأ في قراءة التقرير:', error.message);
}
