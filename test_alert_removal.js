// Test Alert Removal from Reports
console.log('🔥 Testing Alert Removal from Reports');
console.log('='.repeat(50));

async function testAlertRemoval() {
    try {
        console.log('\n1. Loading BugBountyCore...');
        
        // Clear require cache
        const path = require('path');
        const filePath = './assets/modules/bugbounty/BugBountyCore.js';
        delete require.cache[path.resolve(filePath)];
        
        // Load BugBountyCore
        const BugBountyCore = require(filePath);
        const core = new BugBountyCore();
        
        console.log('✅ SUCCESS: BugBountyCore loaded and instance created');
        
        console.log('\n2. Testing cleanAlertsFromContent method...');
        
        // Test content with alerts
        const testContent = `
            <script>alert('Security Test')</script>
            <img src=x onerror=alert("XSS")>
            <div>Some content with alert('test') in it</div>
            <p>Normal content without alerts</p>
        `;
        
        const cleanedContent = core.cleanAlertsFromContent(testContent);
        
        console.log('Original content length:', testContent.length);
        console.log('Cleaned content length:', cleanedContent.length);
        
        // Check if alerts were removed
        const hasAlerts = cleanedContent.includes('alert(');
        if (hasAlerts) {
            console.log('❌ FAILED: Alerts still present in cleaned content');
            console.log('Cleaned content:', cleanedContent);
            return false;
        } else {
            console.log('✅ SUCCESS: All alerts removed from content');
        }
        
        console.log('\n3. Testing formatSinglePageReport with XSS vulnerabilities...');
        
        // Create test data with XSS vulnerabilities
        const testPageData = {
            page_name: 'Test Report',
            page_url: 'https://test.example.com',
            vulnerabilities: [
                {
                    name: 'Cross-Site Scripting (XSS)',
                    type: 'XSS',
                    severity: 'High',
                    payload: '<script>alert("XSS Test")</script>',
                    description: 'XSS vulnerability found with alert("test") payload',
                    evidence: 'JavaScript alert("XSS") executed successfully'
                },
                {
                    name: 'Reflected XSS',
                    type: 'XSS Reflected',
                    severity: 'Medium',
                    payload: '<img src=x onerror=alert("XSS2")>',
                    description: 'Reflected XSS with alert() function',
                    evidence: 'Browser executed alert("reflected") code'
                }
            ]
        };
        
        console.log('Creating report with XSS vulnerabilities...');
        const report = await core.formatSinglePageReport(testPageData);
        
        console.log('Report generated, length:', report.length);
        
        // Check if the report contains alerts
        const reportHasAlerts = report.includes('alert(');
        if (reportHasAlerts) {
            console.log('❌ FAILED: Report still contains alerts');
            
            // Find and show alert occurrences
            const alertMatches = report.match(/alert\([^)]*\)/gi);
            if (alertMatches) {
                console.log('Found alerts in report:', alertMatches.slice(0, 5)); // Show first 5
            }
            return false;
        } else {
            console.log('✅ SUCCESS: No alerts found in generated report');
        }
        
        console.log('\n4. Testing console.log replacement...');
        
        // Check if alerts were replaced with console.log
        const hasConsoleLog = report.includes('console.log');
        if (hasConsoleLog) {
            console.log('✅ SUCCESS: Alerts were replaced with console.log');
        } else {
            console.log('⚠️ WARNING: No console.log found (alerts might have been completely removed)');
        }
        
        console.log('\n5. Final verification...');
        
        // Test the removeAllInlineStylesFromContent method which also cleans alerts
        const testHtml = '<div style="color:red">Test <script>alert("test")</script> content</div>';
        const cleanedHtml = core.removeAllInlineStylesFromContent(testHtml);
        
        const finalHasAlerts = cleanedHtml.includes('alert(');
        const finalHasStyles = cleanedHtml.includes('style=');
        
        if (!finalHasAlerts && !finalHasStyles) {
            console.log('✅ SUCCESS: Both alerts and inline styles removed');
        } else {
            console.log('❌ FAILED: Still contains alerts or styles');
            console.log('Has alerts:', finalHasAlerts);
            console.log('Has styles:', finalHasStyles);
            return false;
        }
        
        console.log('\n' + '='.repeat(50));
        console.log('🎉 FINAL RESULT: ALL ALERT REMOVAL TESTS PASSED!');
        console.log('✅ No more alert popups will appear in reports');
        console.log('✅ XSS payloads use console.log instead of alert');
        console.log('✅ Reports are clean and professional');
        console.log('='.repeat(50));
        
        return true;
        
    } catch (error) {
        console.log('\n❌ FINAL RESULT: TEST FAILED!');
        console.log('Error:', error.message);
        console.log('Stack:', error.stack);
        return false;
    }
}

// Run the test
testAlertRemoval().then(success => {
    if (success) {
        console.log('\n🎊 ALERT REMOVAL CONFIRMED: No more popups in reports!');
        process.exit(0);
    } else {
        console.log('\n💥 ALERT REMOVAL FAILED: Popups may still appear!');
        process.exit(1);
    }
}).catch(error => {
    console.log('\n💥 TEST EXECUTION FAILED:', error.message);
    process.exit(1);
});
