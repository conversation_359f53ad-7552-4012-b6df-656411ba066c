<!DOCTYPE html>
<html>
<head>
    <title>🔍 اختبار عرض الصور من المجلد</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .vulnerability { 
            border: 1px solid #ddd; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px;
            background: #fff;
        }
        .screenshot-section { 
            margin: 15px 0; 
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .image-row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .image-container {
            text-align: center;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            background: white;
            min-width: 300px;
        }
        img { 
            max-width: 300px; 
            max-height: 200px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .test-results {
            margin-top: 30px;
            padding: 20px;
            background: #e9ecef;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار عرض الصور من المجلد</h1>
        <p><strong>الهدف:</strong> التحقق من أن الصور تُعرض من المجلد مباشرة وليس من base64</p>
        
        <div class="vulnerability">
            <h2>🎯 اختبار الصور الموجودة في المجلد</h2>
            
            <div class="screenshot-section">
                <h3>📸 الصور من مجلد report_1752769591090_bv9ppk0hc:</h3>
                
                <div class="image-row">
                    <div class="image-container">
                        <h4>📷 قبل الاستغلال</h4>
                        <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/before_XSS.png" 
                             alt="قبل الاستغلال" 
                             onload="showSuccess(this, 'تم تحميل صورة قبل الاستغلال')"
                             onerror="showError(this, 'فشل تحميل صورة قبل الاستغلال')">
                        <div class="status" id="status-1">⏳ جاري التحميل...</div>
                    </div>
                    
                    <div class="image-container">
                        <h4>⚡ أثناء الاستغلال</h4>
                        <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/during_XSS.png" 
                             alt="أثناء الاستغلال"
                             onload="showSuccess(this, 'تم تحميل صورة أثناء الاستغلال')"
                             onerror="showError(this, 'فشل تحميل صورة أثناء الاستغلال')">
                        <div class="status" id="status-2">⏳ جاري التحميل...</div>
                    </div>
                    
                    <div class="image-container">
                        <h4>🚨 بعد الاستغلال</h4>
                        <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/after_XSS.png" 
                             alt="بعد الاستغلال"
                             onload="showSuccess(this, 'تم تحميل صورة بعد الاستغلال')"
                             onerror="showError(this, 'فشل تحميل صورة بعد الاستغلال')">
                        <div class="status" id="status-3">⏳ جاري التحميل...</div>
                    </div>
                </div>
            </div>
            
            <div class="screenshot-section">
                <h3>📸 صور كبيرة من المجلد:</h3>
                
                <div class="image-row">
                    <div class="image-container">
                        <h4>🖼️ صورة كبيرة 1</h4>
                        <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/screenshot_report_1752769591090_bv9ppk0hc_20250717_200028.png" 
                             alt="صورة كبيرة 1"
                             onload="showSuccess(this, 'تم تحميل الصورة الكبيرة 1')"
                             onerror="showError(this, 'فشل تحميل الصورة الكبيرة 1')">
                        <div class="status" id="status-4">⏳ جاري التحميل...</div>
                    </div>
                    
                    <div class="image-container">
                        <h4>🖼️ صورة كبيرة 2</h4>
                        <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/screenshot_report_1752769591090_bv9ppk0hc_20250717_200033.png" 
                             alt="صورة كبيرة 2"
                             onload="showSuccess(this, 'تم تحميل الصورة الكبيرة 2')"
                             onerror="showError(this, 'فشل تحميل الصورة الكبيرة 2')">
                        <div class="status" id="status-5">⏳ جاري التحميل...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-results">
            <h3>📊 نتائج الاختبار:</h3>
            <div id="results">⏳ جاري اختبار تحميل الصور...</div>
        </div>
    </div>
    
    <script>
        let loadedCount = 0;
        let errorCount = 0;
        let totalImages = 5;
        
        function showSuccess(img, message) {
            const statusDiv = img.nextElementSibling;
            statusDiv.textContent = `✅ ${message}`;
            statusDiv.className = 'status success';
            loadedCount++;
            updateResults();
            console.log(`✅ ${message} من: ${img.src}`);
        }
        
        function showError(img, message) {
            const statusDiv = img.nextElementSibling;
            statusDiv.textContent = `❌ ${message}`;
            statusDiv.className = 'status error';
            errorCount++;
            updateResults();
            console.log(`❌ ${message} من: ${img.src}`);
        }
        
        function updateResults() {
            const resultsDiv = document.getElementById('results');
            const processed = loadedCount + errorCount;
            
            if (processed === totalImages) {
                resultsDiv.innerHTML = `
                    <h4>📈 النتائج النهائية:</h4>
                    <p><strong>✅ تم تحميلها بنجاح:</strong> ${loadedCount} من ${totalImages}</p>
                    <p><strong>❌ فشل التحميل:</strong> ${errorCount} من ${totalImages}</p>
                    <p><strong>📊 معدل النجاح:</strong> ${Math.round((loadedCount/totalImages)*100)}%</p>
                    ${loadedCount > 0 ? '<p style="color: green;"><strong>🎉 الإصلاح نجح! الصور تُعرض من المجلد مباشرة</strong></p>' : '<p style="color: red;"><strong>⚠️ الصور لا تُعرض من المجلد</strong></p>'}
                `;
            } else {
                resultsDiv.innerHTML = `
                    <p>⏳ جاري الاختبار... (${processed}/${totalImages})</p>
                    <p>✅ نجح: ${loadedCount} | ❌ فشل: ${errorCount}</p>
                `;
            }
        }
        
        console.log('🔍 بدء اختبار تحميل الصور من المجلد...');
        console.log(`📊 إجمالي الصور للاختبار: ${totalImages}`);
        
        // اختبار إضافي بعد 3 ثوان
        setTimeout(() => {
            console.log('📊 ملخص نهائي:');
            console.log(`   - الصور المحملة: ${loadedCount}`);
            console.log(`   - الصور الفاشلة: ${errorCount}`);
            console.log(`   - معدل النجاح: ${Math.round((loadedCount/totalImages)*100)}%`);
            
            if (loadedCount > 0) {
                console.log('🎉 الإصلاح نجح! النظام يعرض الصور من المجلد');
            } else {
                console.log('⚠️ الإصلاح لم ينجح، الصور لا تُعرض من المجلد');
            }
        }, 3000);
    </script>
</body>
</html>
