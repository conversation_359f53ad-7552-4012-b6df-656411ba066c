// Simple PowerShell test for BugBountyCore is not defined issue
console.log('Testing BugBountyCore fix...');

try {
    // Test loading BugBountyCore
    console.log('Loading BugBountyCore...');
    const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
    
    // Check if defined
    if (typeof BugBountyCore === 'undefined') {
        console.log('ERROR: BugBountyCore is not defined');
        process.exit(1);
    }
    
    // Check if function
    if (typeof BugBountyCore !== 'function') {
        console.log('ERROR: BugBountyCore is not a function - type: ' + typeof BugBountyCore);
        process.exit(1);
    }
    
    console.log('SUCCESS: BugBountyCore is defined and is a function');
    console.log('Type: ' + typeof BugBountyCore);
    
    // Test creating instance
    try {
        const core = new BugBountyCore();
        console.log('SUCCESS: Instance created successfully');
        
        // Test basic methods
        if (typeof core.initializeSystem === 'function') {
            console.log('SUCCESS: Basic methods exist');
        } else {
            console.log('WARNING: Some methods may be missing');
        }
        
        console.log('FINAL RESULT: Problem SOLVED completely!');
        console.log('BugBountyCore works correctly');
        
    } catch (constructorError) {
        console.log('ERROR: Failed to create instance: ' + constructorError.message);
        process.exit(1);
    }
    
} catch (error) {
    console.log('ERROR: Failed to load BugBountyCore: ' + error.message);
    
    if (error.message.includes('BugBountyCore is not defined')) {
        console.log('DIAGNOSIS: BugBountyCore is not exported correctly');
    }
    
    process.exit(1);
}

console.log('Test completed successfully');
