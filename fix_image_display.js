const fs = require('fs');
const path = require('path');

console.log('🔧 إصلاح عرض الصور في التقارير...\n');

// قراءة ملف BugBountyCore.js
const filePath = 'assets/modules/bugbounty/BugBountyCore.js';
let content = fs.readFileSync(filePath, 'utf8');

console.log('📊 إحصائيات قبل الإصلاح:');
const base64Matches = content.match(/data:image\/png;base64,/g);
console.log(`   - استخدامات base64: ${base64Matches ? base64Matches.length : 0}`);

// الإصلاحات
let fixCount = 0;

// 1. إصلاح دالة generateRealScreenshotHTML
console.log('\n🔧 إصلاح دالة generateRealScreenshotHTML...');
const oldPattern1 = /src="data:image\/png;base64,\$\{([^}]+)\}"/g;
const newPattern1 = 'src="./assets/modules/bugbounty/screenshots/${this.analysisState?.reportId || \'report\'}/screenshot_${Date.now()}.png" data-base64="${$1}"';

content = content.replace(oldPattern1, (match, base64Var) => {
    fixCount++;
    return `src="./assets/modules/bugbounty/screenshots/\${this.analysisState?.reportId || 'report'}/screenshot_\${Date.now()}.png" data-base64="\${${base64Var}}" onload="this.src='data:image/png;base64,' + this.getAttribute('data-base64')"`;
});

// 2. إصلاح الصور في التقارير الرئيسية
console.log('🔧 إصلاح الصور في التقارير الرئيسية...');
const oldPattern2 = /<img src="data:image\/png;base64,\$\{([^}]+)\}"/g;
content = content.replace(oldPattern2, (match, base64Var) => {
    fixCount++;
    return `<img src="./assets/modules/bugbounty/screenshots/\${this.analysisState?.reportId || 'report'}/\${Date.now()}.png" data-base64="\${${base64Var}}" onload="this.src='data:image/png;base64,' + this.getAttribute('data-base64')" onerror="console.warn('فشل تحميل الصورة، استخدام base64'); this.src='data:image/png;base64,' + this.getAttribute('data-base64')"`;
});

// 3. إصلاح الصور البسيطة
console.log('🔧 إصلاح الصور البسيطة...');
const oldPattern3 = /src="data:image\/png;base64,([^"]+)"/g;
content = content.replace(oldPattern3, (match, base64Data) => {
    fixCount++;
    return `src="./assets/modules/bugbounty/screenshots/default/image_${Date.now()}.png" data-base64="${base64Data}" onload="this.src='data:image/png;base64,' + this.getAttribute('data-base64')"`;
});

// 4. إضافة دالة مساعدة لحفظ الصور
console.log('🔧 إضافة دالة مساعدة لحفظ الصور...');
const helperFunction = `
    // دالة مساعدة لحفظ الصور وإرجاع المسار
    saveImageAndGetPath(base64Data, imageName, reportId) {
        try {
            if (!base64Data || base64Data.length < 100) return null;
            
            const cleanBase64 = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
            const screenshotsDir = path.join(__dirname, 'screenshots', reportId || 'default');
            
            // إنشاء المجلد إذا لم يكن موجوداً
            if (!fs.existsSync(screenshotsDir)) {
                fs.mkdirSync(screenshotsDir, { recursive: true });
            }
            
            const imagePath = path.join(screenshotsDir, \`\${imageName}_\${Date.now()}.png\`);
            fs.writeFileSync(imagePath, cleanBase64, 'base64');
            
            // إرجاع المسار النسبي
            return \`./assets/modules/bugbounty/screenshots/\${reportId || 'default'}/\${path.basename(imagePath)}\`;
        } catch (error) {
            console.warn('فشل في حفظ الصورة:', error.message);
            return null;
        }
    }
`;

// البحث عن مكان مناسب لإدراج الدالة
const insertPoint = content.indexOf('// تحميل صورة كـ base64');
if (insertPoint !== -1) {
    content = content.slice(0, insertPoint) + helperFunction + '\n    ' + content.slice(insertPoint);
    console.log('✅ تم إضافة دالة مساعدة لحفظ الصور');
}

// حفظ الملف المُحدث
fs.writeFileSync(filePath, content);

console.log('\n📊 إحصائيات بعد الإصلاح:');
const newBase64Matches = content.match(/data:image\/png;base64,/g);
console.log(`   - استخدامات base64 المتبقية: ${newBase64Matches ? newBase64Matches.length : 0}`);
console.log(`   - عدد الإصلاحات المطبقة: ${fixCount}`);

console.log('\n🎉 تم إصلاح عرض الصور بنجاح!');
console.log('📝 الآن الصور ستُعرض من المجلد مباشرة مع fallback إلى base64');

// إنشاء ملف اختبار للتحقق من الإصلاح
const testContent = `
<!DOCTYPE html>
<html>
<head>
    <title>اختبار عرض الصور</title>
    <style>
        .image-test { margin: 20px; padding: 15px; border: 1px solid #ddd; }
        img { max-width: 300px; border: 2px solid #28a745; }
    </style>
</head>
<body>
    <h1>اختبار عرض الصور من المجلد</h1>
    
    <div class="image-test">
        <h3>صورة من المجلد:</h3>
        <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/before_XSS.png" 
             alt="صورة اختبار" 
             onerror="console.log('فشل تحميل الصورة من المجلد')">
    </div>
    
    <div class="image-test">
        <h3>صورة كبيرة من المجلد:</h3>
        <img src="./assets/modules/bugbounty/screenshots/report_1752769591090_bv9ppk0hc/screenshot_report_1752769591090_bv9ppk0hc_20250717_200028.png" 
             alt="صورة كبيرة" 
             onerror="console.log('فشل تحميل الصورة الكبيرة')">
    </div>
    
    <script>
        console.log('🔍 اختبار تحميل الصور من المجلد...');
        
        // فحص جميع الصور
        document.querySelectorAll('img').forEach((img, index) => {
            img.onload = () => console.log(\`✅ تم تحميل الصورة \${index + 1} بنجاح\`);
            img.onerror = () => console.log(\`❌ فشل تحميل الصورة \${index + 1}\`);
        });
    </script>
</body>
</html>
`;

fs.writeFileSync('test_image_display.html', testContent);
console.log('📄 تم إنشاء ملف اختبار: test_image_display.html');
