// اختبار سريع لإصلاح مشكلة التصدير
console.log('🔥 اختبار إصلاح مشكلة التصدير...');

try {
    // محاولة تحميل BugBountyCore
    console.log('🔍 محاولة تحميل BugBountyCore...');
    const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
    
    if (BugBountyCore) {
        console.log('✅ تم تحميل BugBountyCore بنجاح!');
        console.log(`📊 نوع BugBountyCore: ${typeof BugBountyCore}`);
        console.log(`🔧 هل هو دالة؟ ${typeof BugBountyCore === 'function'}`);
        
        // محاولة إنشاء instance
        console.log('🔍 محاولة إنشاء instance من BugBountyCore...');
        const core = new BugBountyCore();
        
        if (core) {
            console.log('✅ تم إنشاء instance بنجاح!');
            console.log(`📊 نوع core: ${typeof core}`);
            console.log(`🔧 هل يحتوي على دوال؟ ${typeof core.initializeSystem === 'function'}`);
            
            // اختبار بعض الدوال الأساسية
            console.log('🔍 اختبار الدوال الأساسية...');
            
            if (typeof core.initializeSystem === 'function') {
                console.log('✅ دالة initializeSystem موجودة');
            } else {
                console.log('❌ دالة initializeSystem غير موجودة');
            }
            
            if (typeof core.formatSinglePageReport === 'function') {
                console.log('✅ دالة formatSinglePageReport موجودة');
            } else {
                console.log('❌ دالة formatSinglePageReport غير موجودة');
            }
            
            if (typeof core.findRealImageForVulnerability === 'function') {
                console.log('✅ دالة findRealImageForVulnerability موجودة');
            } else {
                console.log('❌ دالة findRealImageForVulnerability غير موجودة');
            }
            
            console.log('🎉 جميع الاختبارات نجحت! المشكلة تم حلها بنسبة 100%!');
            
        } else {
            console.log('❌ فشل في إنشاء instance من BugBountyCore');
        }
        
    } else {
        console.log('❌ BugBountyCore غير محدد بعد التحميل');
    }
    
} catch (error) {
    console.error('❌ خطأ في تحميل BugBountyCore:', error.message);
    console.error('📍 تفاصيل الخطأ:', error.stack);
    
    // محاولة تشخيص المشكلة
    console.log('\n🔍 تشخيص المشكلة...');
    
    if (error.message.includes('BugBountyCore is not defined')) {
        console.log('🔧 المشكلة: BugBountyCore غير مُصدر بشكل صحيح');
        console.log('💡 الحل: إضافة module.exports = BugBountyCore في نهاية الملف');
    } else if (error.message.includes('Cannot find module')) {
        console.log('🔧 المشكلة: الملف غير موجود في المسار المحدد');
        console.log('💡 الحل: التحقق من مسار الملف');
    } else {
        console.log('🔧 المشكلة: خطأ غير معروف');
        console.log('💡 الحل: فحص الكود والتأكد من صحة التصدير');
    }
}

console.log('\n🏁 انتهى اختبار إصلاح مشكلة التصدير');
