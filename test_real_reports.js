// Test Real Reports for Alert Issues
console.log('🔥 Testing Real Reports for Alert Issues');
console.log('='.repeat(60));

const fs = require('fs');
const path = require('path');

async function testRealReports() {
    try {
        console.log('\n1. Loading BugBountyCore...');
        
        // Clear require cache
        const filePath = './assets/modules/bugbounty/BugBountyCore.js';
        delete require.cache[path.resolve(filePath)];
        
        // Load BugBountyCore
        const BugBountyCore = require(filePath);
        const core = new BugBountyCore();
        
        console.log('✅ SUCCESS: BugBountyCore loaded and instance created');
        
        console.log('\n2. Creating test data with XSS vulnerabilities...');
        
        // Create test data with XSS vulnerabilities
        const testPageData = {
            page_name: 'Real Test Report',
            page_url: 'https://test.example.com',
            vulnerabilities: [
                {
                    name: 'Cross-Site Scripting (XSS)',
                    type: 'XSS',
                    severity: 'High',
                    payload: '<script>alert("XSS Test")</script>',
                    description: 'XSS vulnerability found with alert("test") payload',
                    evidence: 'JavaScript alert("XSS") executed successfully'
                },
                {
                    name: 'Reflected XSS',
                    type: 'XSS Reflected',
                    severity: 'Medium',
                    payload: '<img src=x onerror=alert("XSS2")>',
                    description: 'Reflected XSS with alert() function',
                    evidence: 'Browser executed alert("reflected") code'
                }
            ]
        };
        
        console.log('\n3. Generating main report...');
        const mainReport = await core.formatSinglePageReport(testPageData);
        
        console.log(`Main report generated, length: ${mainReport.length}`);
        
        // Check for alerts in main report
        const mainAlerts = (mainReport.match(/alert\(/gi) || []).length;
        console.log(`Main report alerts found: ${mainAlerts}`);
        
        if (mainAlerts > 0) {
            console.log('❌ MAIN REPORT: Still contains alerts');
            
            // Find and show alert occurrences
            const alertMatches = mainReport.match(/alert\([^)]*\)/gi);
            if (alertMatches) {
                console.log('Alert examples in main report:', alertMatches.slice(0, 3));
            }
        } else {
            console.log('✅ MAIN REPORT: No alerts found');
        }
        
        console.log('\n4. Generating separate report...');

        // Generate separate report using formatSinglePageReport (same function for both)
        console.log('📄 Using formatSinglePageReport for separate report generation...');
        const separateReport = await core.formatSinglePageReport(testPageData);
        
        console.log(`Separate report generated, length: ${separateReport.length}`);
        
        // Check for alerts in separate report
        const separateAlerts = (separateReport.match(/alert\(/gi) || []).length;
        console.log(`Separate report alerts found: ${separateAlerts}`);
        
        if (separateAlerts > 0) {
            console.log('❌ SEPARATE REPORT: Still contains alerts');
            
            // Find and show alert occurrences
            const alertMatches = separateReport.match(/alert\([^)]*\)/gi);
            if (alertMatches) {
                console.log('Alert examples in separate report:', alertMatches.slice(0, 3));
            }
        } else {
            console.log('✅ SEPARATE REPORT: No alerts found');
        }
        
        console.log('\n5. Saving reports for inspection...');
        
        // Save reports to files for inspection
        fs.writeFileSync('test_main_report.html', mainReport, 'utf8');
        fs.writeFileSync('test_separate_report.html', separateReport, 'utf8');
        
        console.log('✅ Reports saved to test_main_report.html and test_separate_report.html');
        
        console.log('\n6. Final analysis...');
        
        const totalAlerts = mainAlerts + separateAlerts;
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 FINAL RESULTS:');
        console.log(`🔍 Main report alerts: ${mainAlerts}`);
        console.log(`🔍 Separate report alerts: ${separateAlerts}`);
        console.log(`🔍 Total alerts: ${totalAlerts}`);
        
        if (totalAlerts === 0) {
            console.log('🎉 SUCCESS: No alerts found in either report!');
            console.log('✅ Alert removal is working correctly');
            return true;
        } else {
            console.log('❌ FAILED: Alerts still present in reports');
            console.log('💡 Need to fix alert removal in report generation');
            return false;
        }
        
    } catch (error) {
        console.log('\n❌ ERROR during testing:', error.message);
        console.log('Stack:', error.stack);
        return false;
    }
}

// Run the test
testRealReports().then(success => {
    if (success) {
        console.log('\n🎊 REAL REPORTS TEST: PASSED!');
        console.log('✅ Both main and separate reports are clean');
        process.exit(0);
    } else {
        console.log('\n💥 REAL REPORTS TEST: FAILED!');
        console.log('❌ Reports still contain alerts');
        process.exit(1);
    }
}).catch(error => {
    console.log('\n💥 TEST EXECUTION FAILED:', error.message);
    process.exit(1);
});
